<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.clj.fastble">

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!-- Andre<PERSON>: added permission for bluetooth connect -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

    <!-- flag, that android app can only be isntalled, if ble is supported on phone -->
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="true"/>

</manifest>
