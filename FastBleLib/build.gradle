apply plugin: 'com.android.library'

android {

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    compileSdkVersion 35
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.clj.fastble'
    lint {
        disable 'MissingTranslation'
    }
}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.annotation:annotation-jvm:1.9.1'
    implementation 'androidx.core:core:1.16.0'
    implementation 'androidx.core:core:1.13.0'
}
