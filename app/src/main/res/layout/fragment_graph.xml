<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".graph_fragment">

    <RelativeLayout
        android:id="@+id/graph_actionbar"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#F6F6F6">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@id/img_blue"
            android:gravity="center_vertical"
            android:orientation="vertical"
            tools:ignore="NotSibling">

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:id="@+id/testText"
            android:text=""
            android:textSize="5sp"
            android:layout_height="match_parent"/>

        <LinearLayout
            android:id="@+id/layout_connected"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_save_record"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@drawable/ic_baseline_stop_24"
                android:textSize="0sp" />

            <Button
                android:id="@+id/btn_record"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@drawable/ic_baseline_fiber_manual_record_24"
                android:textSize="0sp" />


        </LinearLayout>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/addDevicesReminder"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_marginTop="50dp"
        android:background="#E9F4FF">

        <ImageView
            android:id="@+id/addDevicesLogo"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            app:srcCompat="@drawable/ic_baseline_add_circle_outline_24" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/addDevicesLogo"
            android:layout_centerHorizontal="true"
            android:text="Add Devices" />
    </RelativeLayout>


    <ScrollView
        android:layout_width="fill_parent"
        android:layout_marginTop = "50dp"
        android:layout_height="fill_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingTop="30dp"
            android:paddingBottom="60dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp">

            <TextView
                android:id="@+id/textview_bpm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="HR: -- bpm"
                android:textSize="18sp"
                android:textColor="@android:color/black"
                android:layout_marginTop="2dp"
                android:layout_marginStart="16dp"
                android:layout_gravity="start"
                android:layout_alignParentTop="true"
                />

            <TextView
                android:id="@+id/YLabel1"
                android:layout_width="@dimen/chartHeight1"
                android:layout_height="20dp"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:textSize="@dimen/labelTextSize"
                android:textAlignment="center"
                android:layout_marginStart="-60dp"
                android:layout_marginTop="75dp"
                android:rotation="270"
                android:text="IR LED"
                android:gravity="center_horizontal"
                android:layout_marginLeft="-60dp" />

            <TextView
                android:id="@+id/YLabel2"
                android:layout_width="@dimen/chartHeight1"
                android:layout_height="20dp"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:textSize="@dimen/labelTextSize"
                android:textAlignment="center"
                android:layout_marginStart="-60dp"
                android:layout_marginTop="265dp"
                android:rotation="270"
                android:text="RED LED"
                android:layout_marginLeft="-60dp"
                android:gravity="center_horizontal" />

            <TextView
                android:id="@+id/YLabel3"
                android:layout_width="@dimen/chartHeight1"
                android:layout_height="20dp"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:textSize="@dimen/labelTextSize"
                android:textAlignment="center"
                android:layout_marginStart="-60dp"
                android:layout_marginTop="450dp"
                android:rotation="270"
                android:text="ACC"
                android:layout_marginLeft="-60dp"
                android:gravity="center_horizontal" />

            <TextView
                android:id="@+id/YLabel4"
                android:layout_width="@dimen/chartHeight1"
                android:layout_height="20dp"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:textSize="@dimen/labelTextSize"
                android:textAlignment="center"
                android:layout_marginStart="-60dp"
                android:layout_marginTop="635dp"
                android:rotation="270"
                android:text="HR (BPM)"
                android:layout_marginLeft="-60dp"
                android:gravity="center_horizontal" />


            <com.github.mikephil.charting.charts.LineChart
                android:id="@+id/chart1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/chartHeight1"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginLeft="20dp"
                android:layout_below="@id/textview_bpm"
                android:textColor="@color/colorTextPrimary" >
            </com.github.mikephil.charting.charts.LineChart>
            <TextView
                android:id="@+id/XLabel1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/labelTextSize"
                android:layout_below="@id/chart1"
                android:paddingLeft="20dp"
                android:layout_centerHorizontal="true"
                android:text="Time(s)" />

            <com.github.mikephil.charting.charts.LineChart
                android:id="@+id/chart2"
                android:layout_width="match_parent"
                android:layout_height="@dimen/chartHeight1"
                android:layout_marginTop="20dp"
                android:layout_below="@id/XLabel1"
                android:layout_marginRight="10dp"
                android:layout_marginLeft="20dp"
                android:textColor="@color/colorTextPrimary" />
            <TextView
                android:id="@+id/XLabel2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/labelTextSize"
                android:layout_below="@id/chart2"
                android:paddingLeft="20dp"
                android:layout_centerHorizontal="true"
                android:text="Time(s)" />

            <com.github.mikephil.charting.charts.LineChart
                android:id="@+id/chart3"
                android:layout_width="match_parent"
                android:layout_height="@dimen/chartHeight1"
                android:layout_marginTop="20dp"
                android:layout_below="@id/XLabel2"
                android:layout_marginRight="10dp"
                android:layout_marginLeft="20dp"
                android:textColor="@color/colorTextPrimary" />
            <TextView
                android:id="@+id/XLabel3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/labelTextSize"
                android:layout_below="@id/chart3"
                android:paddingLeft="20dp"
                android:layout_centerHorizontal="true"
                android:text="Time(s)" />

            <com.github.mikephil.charting.charts.LineChart
                android:id="@+id/chart4"
                android:layout_width="match_parent"
                android:layout_height="@dimen/chartHeight1"
                android:layout_marginTop="20dp"
                android:layout_below="@id/XLabel3"
                android:layout_marginRight="10dp"
                android:layout_marginLeft="20dp"
                android:textColor="@color/colorTextPrimary" />
            <TextView
                android:id="@+id/XLabel4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/labelTextSize"
                android:layout_below="@id/chart4"
                android:paddingLeft="20dp"
                android:layout_centerHorizontal="true"
                android:text="Time(s)" />

        </RelativeLayout>
    </ScrollView>

</RelativeLayout>