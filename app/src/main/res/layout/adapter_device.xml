<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="56dp">

        <ImageView
            android:id="@+id/img_blue"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_centerVertical="true" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@id/img_blue"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txt_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="1dp"
                android:textColor="#000000"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/txt_mac"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="#000000"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_idle"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:id="@+id/txt_rssi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="10sp" />

            <ImageView
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_marginLeft="5dp"
                app:srcCompat="@drawable/ic_baseline_rssi_10" />

            <Button
                android:id="@+id/btn_connect"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="10dp"
                android:background="@drawable/ic_baseline_add_circle_outline_24"
                android:textSize="12sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_connected"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_graph"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@drawable/ic_baseline_play_arrow_24"
                android:textSize="0sp" />

            <Button
                android:id="@+id/btn_detail"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@drawable/ic_baseline_settings_24"
                android:textSize="0sp" />

            <Button
                android:id="@+id/btn_disconnect"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@drawable/ic_baseline_delete_24"
                android:textSize="0sp" />

        </LinearLayout>


    </RelativeLayout>


</LinearLayout>