<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingLeft="15dp">

        <TextView
            android:id="@+id/txt_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/characteristic"
            android:textSize="14sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/txt_uuid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:text="uuid"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/txt_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:text="@string/characteristic"
            android:textSize="12sp" />

    </LinearLayout>


</LinearLayout>