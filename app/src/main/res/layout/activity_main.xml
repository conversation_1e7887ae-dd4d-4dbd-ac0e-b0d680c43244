<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:title="@string/title_device"
        app:titleTextColor="@android:color/white" />

    <LinearLayout
        android:id="@+id/layout_setting"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/toolbar"
        android:background="#eeeeee"
        android:orientation="vertical"
        android:padding="20dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="10dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:text="@string/scan_setting"
            android:textColor="@color/colorPrimaryDark"
            android:textSize="12sp" />

        <EditText
            android:id="@+id/et_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/setting_name"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:textSize="13sp" />

        <EditText
            android:id="@+id/et_mac"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/setting_mac"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:textSize="13sp" />

        <EditText
            android:id="@+id/et_uuid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/setting_uuid"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:textSize="13sp" />

        <Switch
            android:id="@+id/sw_auto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:text="AutoConnect ?"
            android:textColor="#7E7E7E"
            android:textSize="13sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/txt_setting"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/layout_setting"
        android:layout_centerInParent="true"
        android:background="#eeeeee"
        android:layout_marginBottom="10dp"
        android:padding="10dp" />

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/txt_setting"></FrameLayout>

    <FrameLayout
        android:id="@+id/fragment_container_data"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/txt_setting"></FrameLayout>

    <ListView
        android:id="@+id/list_device"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="150dp"
        android:layout_below="@id/fragment_container"
        android:divider="@android:color/darker_gray"
        android:dividerHeight="0.5dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:scrollbars="none" >
    </ListView>

    <!-- android:layout_below="@id/txt_setting" -->
    <!--    android:layout_marginBottom="10dp"-->

    <ImageView
        android:id="@+id/img_loading"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_alignBottom="@+id/btn_scan"
        android:layout_alignParentRight="true"
        android:layout_marginTop="10dp"
        android:layout_marginRight="48dp"
        android:layout_marginBottom="12dp"
        app:srcCompat="@drawable/ic_baseline_autorenew_24"
        android:visibility="invisible" />

    <Button
        android:id="@+id/btn_scan"
        style="@style/Widget.AppCompat.Button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:background="@drawable/primary_button"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:layout_above="@+id/nav_view"
        android:layout_marginBottom="30dp"
        android:text="@string/start_scan"
        android:textColor="@color/buttonText" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="0dp"
        android:background="#FFFFFF"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintVertical_bias="0.0"
        app:menu="@menu/bottom_nav_menu"></com.google.android.material.bottomnavigation.BottomNavigationView>


</RelativeLayout>

