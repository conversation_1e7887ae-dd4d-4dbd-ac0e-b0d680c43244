<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:title="@string/app_name"
        app:titleTextColor="@android:color/white" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="25dp"
        android:paddingTop="10dp"
        android:text="Email： <EMAIL>"
        android:textSize="15sp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="25dp"
        android:paddingTop="10dp"
        android:text="QR:"
        android:textSize="15sp" />

    <ImageView
        android:layout_width="180dp"
        android:layout_height="180dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="10dp"
        android:scaleType="centerInside"
        android:src="@drawable/ic_wx" />

</LinearLayout>

