<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/txt_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:textSize="12sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/txt_mac"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:textSize="12sp"
        android:textStyle="bold" />

    <ListView
        android:id="@+id/list_service"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:divider="@android:color/darker_gray"
        android:textColor="#000000"
        android:dividerHeight="0.5dp"
        android:paddingEnd="10dp"
        android:paddingStart="10dp"
        android:scrollbars="none" />


</LinearLayout>