<resources tools:ignore="ExtraTranslation" xmlns:tools="http://schemas.android.com/tools">

    <string name="app_name">Sensor Data Manager</string>

    <string name="notifyTitle">Prompt</string>
    <string name="gpsNotifyMsg">Current mobile phone scanning Bluetooth needs to open the positioning function</string>
    <string name="setting">Go to settings</string>
    <string name="cancel">Cancel</string>

    <string name="start_scan">Start scanning</string>
    <string name="stop_scan">Stop scanning</string>
    <string name="expand_search_settings">Search Filter</string>
    <string name="retrieve_search_settings">Close Filter</string>
    <string name="connect_fail">Connection failed</string>
    <string name="active_disconnected">Disconnected</string>
    <string name="disconnected">Connection disconnect</string>
    <string name="please_open_blue">Please turn on Bluetooth first</string>
    <string name="select_operation_type">Select operation type</string>
    <string name="characteristic">Characteristic</string>
    <string name="data_changed">Data changes:</string>
    <string name="read">Read</string>
    <string name="write">Write</string>
    <string name="open_notification">Open notification</string>
    <string name="close_notification">Close notification</string>
    <string name="service_list">Service list</string>
    <string name="characteristic_list">Characteristic list</string>
    <string name="console">Console</string>
    <string name="name">Device broadcast name：</string>
    <string name="mac">MAC:</string>
    <string name="service">Service</string>
    <string name="type">Service type (main service)</string>
    <string name="scan_setting">Below, you can configure the conditions that you need to scan the device, which can be empty</string>
    <string name="setting_name">Enter a broadcast name for Bluetooth devices, separated by English commas</string>
    <string name="setting_mac">Enter Bluetooth device MAC</string>
    <string name="setting_uuid">Enter Bluetooth device UUID, separated by commas in English</string>
    <string name="input_hex">Please enter the HEX format command</string>
    <string name="connect">Connect</string>
    <string name="connected">Connected</string>
    <string name="disconnect">Disconnect</string>
    <string name="enter">Enter</string>

    <string name="hello_blank_fragment">Ready to connect</string>

    <string name="title_device">Devices</string>
    <string name="title_graph">Graph</string>
    <string name="title_data">Data</string>

</resources>
