package com.clj.bles<PERSON><PERSON><PERSON>;

import android.Manifest;
import android.app.AlertDialog;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattService;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;

import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.util.Log;

import android.text.Editable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.clj.blesamplePCBA.adapter.DeviceAdapter;
import com.clj.blesamplePCBA.SignalProcessor;
import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleNotifyCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.listener.OnChartValueSelectedListener;
import com.github.mikephil.charting.utils.ColorTemplate;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Deque;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
//SPINNER
import androidx.annotation.RequiresApi;
import androidx.annotation.RequiresPermission;
import androidx.fragment.app.Fragment;

public class graph_fragment extends Fragment implements View.OnClickListener {

    private static final int BUFFER_SIZE = 200;
    private static final int SAMPLE_RATE_HZ = 50;
    private static final int PROCESS_INTERVAL_MS = 1500;

    private final Deque<Integer> irBuffer = new ArrayDeque<>();
    private final Deque<Integer> redBuffer = new ArrayDeque<>();
    private final Deque<Integer> accBuffer = new ArrayDeque<>();

    // Buffers for cleaned signals (for plotting) - using RED as primary PPG signal
    private final Deque<Float> cleanedRedBuffer = new ArrayDeque<>();
    private final Deque<Float> cleanedIrBuffer = new ArrayDeque<>();  // Keep for compatibility

    private Handler processingHandler;
    private Runnable processingRunnable;

    // Heart rate tracking
    private volatile float currentHeartRate = 0f;

    private TextView bpmTextView;

    private static final String TAG = "graph_fragment TAG";
    private static final int LONG_DELAY = 3500; // 3.5 seconds
    private static final int SHORT_DELAY = 2000; // 2 seconds
    public final static UUID UUID_SERVICE = UUID.fromString("4fafc201-1fb5-459e-8fcc-c5c9c331914b");
    public final static UUID UUID_CHARACTERISTIC = UUID.fromString("beb5483e-36e1-4688-b7f5-ea07361b26a8");
    private boolean graphStatus;
    private boolean displayGraphStatus;

    private float xValueTemp;
    private float previousTime;
    private float currentTime;
    private int dataSetIndex;

    private LineChart chart1;
    private LineChart chart2;
    private LineChart chart3;
    private LineChart chart4;  // Heart rate chart

    // Store all data into 3D arraylist
    ArrayList<ArrayList<ArrayList<String>>> csvData;

    private TextView txt_test;
    private Button btn_record;
    private RelativeLayout addDevicesReminder;
    private Button btn_stop;

    private DeviceAdapter mDeviceAdapter;
    private DeviceAdapter connectedDeviceAdapter;
    private List<BluetoothGattService> serviceList;
    private List<BluetoothGattCharacteristic> characteristicList;

    List<BleDevice> connectedDevices;
    Map<String, Integer> mymap;
    Map<Integer, String> mymapName;


    // filter parameters and necessary classes

    float alpha = 0.927f; // tuned for 0.3Hz cutoff with 25ms sample rate

    int windowSize = 5;
    int chartUpdateCounter=0;


    private Map<Integer, Float> lastIRInput = new HashMap<>();

    private Map<Integer, Float> lastIROutput = new HashMap<>();

    private Map<Integer, Float> lastREDInput = new HashMap<>();

    private Map<Integer, Float> lastREDOutput = new HashMap<>();

    private Map<Integer, List<Float>> irBufferMap = new HashMap<>();



    // list of data samples received from the buffer and use to update the graphs


    List<Entry> irEntries = new ArrayList<>();
    List<Entry> redEntries = new ArrayList<>();
    List<Entry> accEntries = new ArrayList<>();

    boolean textFileInit = false;
    long lastTimestamp = -1;

    ArrayList<ArrayList<String>> data_txt = new ArrayList<>();

    ArrayList<String> header = new ArrayList<>();

    public void exportCSV(View v, String txtFileName, ArrayList<ArrayList<String>> data_txt) {
        try {
            Context context = getActivity().getApplicationContext();
            String path = "";

            // Create target folder
            File documents = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
            final String folderName = "ArduNet_TXT_measurements";
            File folder = new File(documents, folderName);
            if (!folder.exists()) {
                boolean created = folder.mkdirs();
                Log.d("Storage", "Folder created: " + created);
            }

            // Convert data to plain text format
            StringBuilder data = new StringBuilder();
            for (ArrayList<String> row : data_txt) {
                for (int i = 0; i < row.size(); i++) {
                    data.append(row.get(i));
                    if (i < row.size() - 1) {
                        data.append("\t"); // tab-separated for text file
                    }
                }
                data.append("\n");
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                ContentValues values = new ContentValues();
                values.put(MediaStore.Downloads.DISPLAY_NAME, txtFileName + ".txt");
                values.put(MediaStore.Downloads.MIME_TYPE, "text/plain");
                values.put(MediaStore.Downloads.IS_PENDING, 1);
                values.put(MediaStore.Downloads.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS + "/" + folderName);

                ContentResolver resolver = context.getContentResolver();
                Uri collection = MediaStore.Downloads.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY);
                Uri fileUri = resolver.insert(collection, values);
                path = fileUri.toString();

                try (OutputStream out = resolver.openOutputStream(fileUri)) {
                    out.write(data.toString().getBytes());
                    out.flush();
                    Toast.makeText(context, "Saved to Downloads/" + folderName, Toast.LENGTH_SHORT).show();
                }

                values.clear();
                values.put(MediaStore.Downloads.IS_PENDING, 0);
                resolver.update(fileUri, values, null, null);
            } else {
                File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
                File fullDir = new File(downloadsDir, folderName);
                File file = new File(fullDir, txtFileName + ".txt");
                path = file.getAbsolutePath();

                try (FileOutputStream out = new FileOutputStream(file)) {
                    out.write(data.toString().getBytes());
                    Toast.makeText(context, "Saved to Downloads/" + folderName, Toast.LENGTH_SHORT).show();
                }
            }

            // Optional: share the file
            Intent fileIntent = new Intent(Intent.ACTION_SEND);
            fileIntent.setType("text/plain");
            fileIntent.putExtra(Intent.EXTRA_SUBJECT, txtFileName);
            fileIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            if (!path.equals("")) {
                fileIntent.putExtra(Intent.EXTRA_STREAM, Uri.parse(path));
            }
            startActivity(Intent.createChooser(fileIntent, "Send file"));

        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(getActivity(), "Error: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    boolean notificationEnabled = false;

    private void parseAndBufferSamples(byte[] data) {
        try {
            int sampleSize = 7;
            int sampleCount = data.length / sampleSize;

            for (int i = 0; i < sampleCount; i++) {
                int offset = i * sampleSize;

                int ir = ((data[offset] & 0xFF) << 8) | (data[offset + 1] & 0xFF);
                int red = ((data[offset + 2] & 0xFF) << 8) | (data[offset + 3] & 0xFF);
                int acc = ((data[offset + 4] & 0xFF) << 8) | (data[offset + 5] & 0xFF);

                synchronized (irBuffer) {
                    if (irBuffer.size() >= BUFFER_SIZE) irBuffer.pollFirst();
                    if (redBuffer.size() >= BUFFER_SIZE) redBuffer.pollFirst();
                    if (accBuffer.size() >= BUFFER_SIZE) accBuffer.pollFirst();
                    irBuffer.addLast(ir);
                    redBuffer.addLast(red);
                    accBuffer.addLast(acc);

                    // Process signals in real-time for plotting
                    processRealtimeSignals(ir, red, acc);
                }

                // Optionally update graph here if needed
            }
        } catch (Exception e) {
            Log.e("PPG_PARSE", "Error while parsing data: " + e.getMessage());
        }
    }

    private List<Integer> detectPeaks(float[] signal, float sampleRateHz) {
        // Enhanced peak detection with multiple techniques
        float[] s = smooth(signal, 3);  // Reduced smoothing to preserve peak sharpness

        // Calculate adaptive threshold based on signal statistics
        float mean = 0f, std = 0f;
        for (float v : s) mean += v;
        mean /= s.length;

        for (float v : s) std += (v - mean) * (v - mean);
        std = (float) Math.sqrt(std / s.length);

        // Adaptive threshold: mean + k * std (k typically 1.5-3.0)
        float adaptiveThresh = mean + 2.0f * std;

        // Also use percentile-based threshold as backup
        float[] sortedSignal = s.clone();
        java.util.Arrays.sort(sortedSignal);
        float percentileThresh = sortedSignal[(int)(0.85f * sortedSignal.length)]; // 85th percentile

        // Use the higher of the two thresholds
        float thresh = Math.max(adaptiveThresh, percentileThresh);

        // Minimum distance between peaks (300ms for heart rate)
        int minDist = (int)(0.3f * sampleRateHz);

        // Calculate first and second derivatives for better peak detection
        float[] firstDeriv = new float[s.length - 1];
        float[] secondDeriv = new float[s.length - 2];

        for (int i = 0; i < firstDeriv.length; i++) {
            firstDeriv[i] = s[i + 1] - s[i];
        }

        for (int i = 0; i < secondDeriv.length; i++) {
            secondDeriv[i] = firstDeriv[i + 1] - firstDeriv[i];
        }

        List<Integer> peaks = new ArrayList<>();
        int lastPeak = -minDist;

        // Enhanced peak detection using multiple criteria
        for (int i = 2; i < s.length - 2; i++) {
            // Basic zero-crossing of first derivative
            boolean zeroCross = (i < firstDeriv.length - 1) &&
                               (firstDeriv[i - 1] > 0 && firstDeriv[i] <= 0);

            // Amplitude threshold
            boolean aboveThresh = s[i] > thresh;

            // Distance constraint
            boolean farEnough = (i - lastPeak) >= minDist;

            // Local maximum check (more robust)
            boolean isLocalMax = s[i] > s[i - 1] && s[i] > s[i + 1] &&
                                s[i] > s[i - 2] && s[i] > s[i + 2];

            // Second derivative check (concave down at peak)
            boolean concaveDown = (i - 1 < secondDeriv.length) && secondDeriv[i - 1] < 0;

            // Prominence check - peak should be significantly higher than surrounding valleys
            float leftMin = Float.MAX_VALUE, rightMin = Float.MAX_VALUE;
            int searchRange = Math.min(minDist / 2, 10);

            for (int j = Math.max(0, i - searchRange); j < i; j++) {
                leftMin = Math.min(leftMin, s[j]);
            }
            for (int j = i + 1; j < Math.min(s.length, i + searchRange + 1); j++) {
                rightMin = Math.min(rightMin, s[j]);
            }

            float prominence = s[i] - Math.max(leftMin, rightMin);
            boolean hasProminence = prominence > std * 0.5f; // Prominence threshold

            // Combine criteria for robust peak detection
            if ((zeroCross || isLocalMax) && aboveThresh && farEnough &&
                concaveDown && hasProminence) {
                peaks.add(i);
                lastPeak = i;
            }
        }

        // Post-processing: remove peaks that are too close or too weak
        List<Integer> filteredPeaks = new ArrayList<>();
        for (int i = 0; i < peaks.size(); i++) {
            int peakIdx = peaks.get(i);
            boolean keepPeak = true;

            // Check if this peak is the strongest in its neighborhood
            for (int j = i + 1; j < peaks.size(); j++) {
                int nextPeakIdx = peaks.get(j);
                if (nextPeakIdx - peakIdx < minDist) {
                    // Two peaks too close - keep the stronger one
                    if (s[nextPeakIdx] > s[peakIdx]) {
                        keepPeak = false;
                        break;
                    }
                } else {
                    break;
                }
            }

            if (keepPeak) {
                filteredPeaks.add(peakIdx);
            }
        }

        return filteredPeaks;
    }


    /**
     * Simple moving‐average smoother
     */
    private float[] smooth(float[] x, int windowSize) {
        float[] y = new float[x.length];
        float sum = 0f;
        int half = windowSize / 2;

        // Initialize sum for the first window
        for (int i = 0; i < windowSize && i < x.length; i++) {
            sum += x[i];
            y[i] = sum / (i + 1);
        }

        // Slide the window
        for (int i = windowSize; i < x.length; i++) {
            sum += x[i] - x[i - windowSize];
            y[i - half] = sum / windowSize;
        }

        // Fill the tail
        for (int i = x.length - half; i < x.length; i++) {
            y[i] = y[x.length - half - 1];
        }

        return y;
    }

    /**
     * Process signals in real-time for plotting (maintains sliding window)
     */
    private void processRealtimeSignals(int rawIr, int rawRed, int rawAcc) {
        // Use a smaller window for real-time processing (e.g., 25 samples = 0.5 seconds at 50Hz)
        final int REALTIME_WINDOW = 25;

        // Only process when we have enough samples
        if (redBuffer.size() >= REALTIME_WINDOW) {
            // Convert recent samples to arrays for processing - using RED as primary PPG signal
            float[] redWindow = new float[REALTIME_WINDOW];
            float[] accWindow = new float[REALTIME_WINDOW];

            synchronized (irBuffer) {
                Integer[] redArray = redBuffer.toArray(new Integer[0]);
                Integer[] accArray = accBuffer.toArray(new Integer[0]);

                // Get the most recent REALTIME_WINDOW samples
                int startIdx = Math.max(0, redArray.length - REALTIME_WINDOW);
                for (int i = 0; i < REALTIME_WINDOW; i++) {
                    redWindow[i] = redArray[startIdx + i].floatValue();
                    accWindow[i] = accArray[startIdx + i].floatValue();
                }
            }

            // Apply C++ signal processing - using RED as primary PPG signal
            try {
                float[] cleanedRed = SignalProcessor.runNativePipeline(redWindow, accWindow, SAMPLE_RATE_HZ);

                // Store the most recent cleaned sample
                synchronized (cleanedRedBuffer) {
                    if (cleanedRedBuffer.size() >= BUFFER_SIZE) cleanedRedBuffer.pollFirst();
                    if (cleanedIrBuffer.size() >= BUFFER_SIZE) cleanedIrBuffer.pollFirst();

                    // Use the last processed sample - RED is now the primary cleaned signal
                    float cleanedValue = cleanedRed[cleanedRed.length - 1];
                    cleanedRedBuffer.addLast(cleanedValue);
                    // Keep IR as raw for compatibility
                    cleanedIrBuffer.addLast((float) rawIr);

                    // Log for debugging (remove in production)
                    if (cleanedRedBuffer.size() % 50 == 0) { // Log every 50 samples
                        Log.d("SIGNAL_PROCESSING", String.format("Raw RED: %.2f, Cleaned RED: %.2f, Buffer size: %d",
                              (float) rawRed, cleanedValue, cleanedRedBuffer.size()));
                    }
                }
            } catch (Exception e) {
                Log.e("SIGNAL_PROCESSING", "Error in real-time processing: " + e.getMessage());
                e.printStackTrace();
                // Fallback to raw values
                synchronized (cleanedRedBuffer) {
                    if (cleanedRedBuffer.size() >= BUFFER_SIZE) cleanedRedBuffer.pollFirst();
                    if (cleanedIrBuffer.size() >= BUFFER_SIZE) cleanedIrBuffer.pollFirst();
                    cleanedRedBuffer.addLast((float) rawRed);
                    cleanedIrBuffer.addLast((float) rawIr);
                }
            }
        }
    }

    private void processPpgSignal() {
        // 1) Copy buffers - using RED as primary PPG signal
        List<Float> redCopy = new ArrayList<>();
        List<Float> accCopy = new ArrayList<>();
        synchronized (irBuffer) {
            if (redBuffer.size() < BUFFER_SIZE) return;
            for (int v : redBuffer) redCopy.add((float)v);
            for (int v : accBuffer) accCopy.add((float)v);
        }

        // 2) To arrays
        int N = redCopy.size();
        float[] redArr  = new float[N];
        float[] accArr = new float[N];
        for (int i = 0; i < N; i++) {
            redArr[i]  = redCopy.get(i);
            accArr[i] = accCopy.get(i);
        }

        // 3) Native cleanup @ 50 Hz - using RED signal
        float[] cleaned = SignalProcessor.runNativePipeline(redArr, accArr, SAMPLE_RATE_HZ);

        // 4) Peak detection
        List<Integer> peaks = detectPeaks(cleaned, SAMPLE_RATE_HZ);

        // 5) RR intervals → BPM
        if (peaks.size() >= 2) {
            long sum = 0;
            for (int i = 1; i < peaks.size(); i++) {
                int deltaSamples = peaks.get(i) - peaks.get(i - 1);
                sum += Math.round(1000f * deltaSamples / SAMPLE_RATE_HZ);
            }
            long avgRR = sum / (peaks.size() - 1);
            if (avgRR > 0) {
                int bpm = Math.round(60000f / avgRR);
                currentHeartRate = (float) bpm;  // Store current heart rate for plotting
                bpmTextView.setText("HR: " + bpm + " bpm");

                // Log for debugging
                Log.d("PEAK_DETECTION", String.format("Detected %d peaks, BPM: %d, Avg RR: %d ms",
                      peaks.size(), bpm, avgRR));
            }
        } else {
            Log.d("PEAK_DETECTION", String.format("Insufficient peaks detected: %d", peaks.size()));
        }
    }



    @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
    private void getData() {
        connectedDeviceAdapter = ((MainActivity)getActivity()).getDeviceAdapter();
        for (int ii = 0; ii < connectedDeviceAdapter.getCount();ii++) {
            final BleDevice tempDevice = connectedDeviceAdapter.getItem(ii); // Get BLE devices from connected list
            if (tempDevice.getGraphStatus() == 1) { // Filter by graph status
                serviceList = BleManager.getInstance().getBluetoothGattServices(tempDevice);
                for (BluetoothGattService gattService:serviceList) { // Parse all services
                    characteristicList = BleManager.getInstance().getBluetoothGattCharacteristics(gattService);
                    if (gattService.getUuid().toString().equalsIgnoreCase(UUID_SERVICE.toString())) { // Filter by UUID
                        for (BluetoothGattCharacteristic gattCharacteristic : characteristicList) { // Parse all characteristics
                            if (gattCharacteristic.getUuid().toString().equalsIgnoreCase(UUID_CHARACTERISTIC.toString())) {
                                if (!notificationEnabled){
                                BleManager.getInstance().notify(
                                        tempDevice,
                                        gattService.getUuid().toString(),
                                        gattCharacteristic.getUuid().toString(),
                                        new BleNotifyCallback() {
                                            @Override
                                            public void onNotifySuccess() {
                                                Log.d("BLE_NOTIFY", "Notification enabled");
                                                notificationEnabled = true;
                                            }

                                            @Override
                                            public void onNotifyFailure(BleException exception) {
                                                Log.e("BLE_NOTIFY", "Failed to enable notification: " + exception.toString());
                                            }

                                            @Override
                                            public void onCharacteristicChanged(byte[] data) {
                                                parseAndBufferSamples(data);

                                                try {
                                                    if (!textFileInit) {
                                                        textFileInit = true;
                                                        header.add("Time Difference");
                                                        header.add("IR");
                                                        header.add("RED");
                                                        header.add("ACC");
                                                        data_txt.add(header);
                                                    }

                                                    if (mymap.get(tempDevice.getMac()) == null) {
                                                        addDevicesReminder.setVisibility(View.GONE);
                                                        ILineDataSet tempDataSet = initializeLineDataSet("IR", Color.BLUE);
                                                        chart1.getData().addDataSet(tempDataSet);
                                                        chart2.getData().addDataSet(initializeLineDataSet("RED", Color.RED));
                                                        chart3.getData().addDataSet(initializeLineDataSet("ACC",Color.GREEN));
                                                        chart4.getData().addDataSet(initializeLineDataSet("HR", Color.MAGENTA));

                                                        dataSetIndex = chart1.getData().getDataSetCount()-1;
                                                        mymap.put(tempDevice.getMac(),dataSetIndex);
                                                        mymapName.put(dataSetIndex,tempDevice.getName());
//                                                        ArrayList<ArrayList<String>> newSheet = new ArrayList<>();
//                                                        ArrayList<String> headers = new ArrayList<>(Arrays.asList("Date", "Time", "IR", "IR filtered", "RED", "ACC x", "ACC y", "ACC z"));
//                                                        newSheet.add(headers);
//                                                        csvData.add(dataSetIndex - 1, newSheet);
                                                    } else {
                                                        dataSetIndex = mymap.get(tempDevice.getMac());
                                                    }

                                                    int sampleSize = 7;
                                                    int sampleCount = data.length / sampleSize;

                                                    for (int i = 0; i < sampleCount; i++) {
                                                        int offset = i * sampleSize;

                                                        int ir = ((data[offset] & 0xFF) << 8) | (data[offset + 1] & 0xFF);
                                                        int red = ((data[offset + 2] & 0xFF) << 8) | (data[offset + 3] & 0xFF);
                                                    //    int acc = (short) (((data[offset + 4] & 0xFF) << 8) | (data[offset + 5] & 0xFF));
                                                        int acc = (((data[offset + 4] & 0xFF) << 8) | (data[offset + 5] & 0xFF));


//                                                        long timestamp = ((data[offset + 6] & 0xFFL) << 24) |
//                                                                ((data[offset + 7] & 0xFFL) << 16) |
//                                                                ((data[offset + 8] & 0xFFL) << 8) |
//                                                                ((data[offset + 9] & 0xFFL));

                                                        long difference = (((data[offset + 6] & 0xFF)));


                                                        //   long timestamp = System.currentTimeMillis();
                                                        ArrayList<String> row = new ArrayList<>();
                                                        row.add(String.valueOf(difference));
                                                        row.add(String.valueOf(ir));
                                                        row.add(String.valueOf(red));
                                                        row.add(String.valueOf(acc));
                                                        data_txt.add(row);

                                                        xValueTemp += 0.05f;

                                                        long currentTime = System.currentTimeMillis();
                                                        if (lastTimestamp != -1) {
                                                            long delta = currentTime - lastTimestamp;
                                                            // Log.d("BLE_Read_Timing", "Time since last read: " + delta + " ms");
                                                        }

                                                        // Use cleaned signals for plotting if available, otherwise use raw
                                                        // RED is now the primary PPG signal for analysis
                                                        float plotIr = (float) ir;  // Keep IR as raw
                                                        float plotRed = (float) red;  // Will be replaced with cleaned if available

                                                        synchronized (cleanedRedBuffer) {
                                                            if (!cleanedRedBuffer.isEmpty()) {
                                                                plotRed = cleanedRedBuffer.peekLast();  // Use cleaned RED signal
                                                            }
                                                            // Keep IR as raw for comparison
                                                        }

                                                        addEntry(chart1, mymap.get(tempDevice.getMac()), xValueTemp, plotIr);  // Raw IR
                                                        addEntry(chart2, mymap.get(tempDevice.getMac()), xValueTemp, plotRed);  // Cleaned RED
                                                        addEntry(chart3, mymap.get(tempDevice.getMac()), xValueTemp, (float) acc);
                                                        addEntry(chart4, mymap.get(tempDevice.getMac()), xValueTemp, currentHeartRate);  // Heart Rate

                                                        lastTimestamp = currentTime;


//                                                        try {
//                                                            Thread.sleep(20);  // This will block the UI thread if used in main thread!
//                                                        } catch (InterruptedException e) {
//                                                            e.printStackTrace();
//                                                        }
                                                    }

                                                } catch (Exception e) {
                                                    Log.e("BLE_NOTIFY_PARSE", "Failed to parse notification data: " + e.getMessage());
                                                }                                            }
                                        }
                                );





                                // Filter by UUID
//                                BleManager.getInstance().read(
//                                        tempDevice,
//                                        gattService.getUuid().toString(),
//                                        gattCharacteristic.getUuid().toString(),
//                                        new BleReadCallback() { // Get broadcast
//                                            @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
//                                            @RequiresApi(api = Build.VERSION_CODES.KITKAT)
//                                            @Override



//                                            public void onReadSuccess(final byte[] data) {
//
//                                                if(!textFileInit)
//                                                {
//                                                    textFileInit = true;
//                                                    header.add("Timestamp");
//                                                    header.add("IR");
//                                                    header.add("RED");
//                                                    header.add("ACC");
//                                                    data_txt.add(header);
//                                                }
//
//                                                if (!mymap.containsKey(tempDevice.getMac())) {
//                                                    addDevicesReminder.setVisibility(View.GONE);
//
//                                                    ILineDataSet tempDataSet = initializeLineDataSet(tempDevice.getName(), getRandomColor());
//                                                    chart1.getData().addDataSet(tempDataSet);
//                                                    chart2.getData().addDataSet(initializeLineDataSet(tempDevice.getName(), getRandomColor()));
//
//                                                    dataSetIndex = chart1.getData().getDataSetCount() - 1;
//                                                    mymap.put(tempDevice.getMac(), dataSetIndex);
//                                                    mymapName.put(dataSetIndex, tempDevice.getName());
//
//                                                    ArrayList<ArrayList<String>> newSheet = new ArrayList<>();
//                                                    ArrayList<String> headers = new ArrayList<>(Arrays.asList("Date", "Time", "IR", "IR filtered", "RED", "ACC x", "ACC y", "ACC z"));
//                                                    newSheet.add(headers);
//                                                    csvData.add(dataSetIndex - 1, newSheet);
//                                                } else {
//                                                    dataSetIndex = mymap.get(tempDevice.getMac());
//                                                }
//
//                                                //txt_test.setText(txt_test.getText() + tempDevice.getMac());
//
//                                                //String str = new String(data, StandardCharsets.UTF_8);
//
//                                               // String[][] allSamples = unpackageBLEPacketMulti(str);
//
//                                                // Each sample is 10 bytes: [IR(2), RED(2), AX(2), AY(2), AZ(2)]
//                                                int sampleSize = 50;
//                                                int sampleCount = data.length / sampleSize;
//
//                                                for (int i = 0; i < sampleCount; i++) {
//                                                    try{
//                                                    int offset = i * sampleSize;
//
//                                                    int ir = ((data[offset] & 0xFF) << 8) | (data[offset + 1] & 0xFF);
//                                                    int red = ((data[offset + 2] & 0xFF) << 8) | (data[offset + 3] & 0xFF);
//                                                    int acc = (short)(((data[offset + 4] & 0xFF) << 8) | (data[offset + 5] & 0xFF));
////                                                    int ay = (short)(((data[offset + 6] & 0xFF) << 8) | (data[offset + 7] & 0xFF));
////                                                    int az = (short)(((data[offset + 8] & 0xFF) << 8) | (data[offset + 9] & 0xFF));
//
//                                                    long timestamp = System.currentTimeMillis();
//
//                                                    ArrayList<String> row = new ArrayList<>();
//                                                    row.add(String.valueOf(timestamp));
//                                                    row.add(String.valueOf(ir));
//                                                    row.add(String.valueOf(red));
//                                                    row.add(String.valueOf(acc));
//
//                                                   //     row.add(String.valueOf(acc));
////                                                    row.add(String.valueOf(ay));
////                                                    row.add(String.valueOf(az));
//
//                                                    data_txt.add(row);
//
//                                                    // Optional: You can also plot here if needed
//                                                    // float accMag = (float)Math.sqrt(ax * ax + ay * ay + az * az);
//                                                    // addEntry(chart, datasetIndex, xValue, accMag);
//                                                    xValueTemp += 0.02f;
//
//
//                                                    long currentTime = System.currentTimeMillis(); // or use timestamp from sensor data if embedded
//
//                                                    if (lastTimestamp != -1) {
//                                                        long delta = currentTime - lastTimestamp;
//                                                        Log.d("BLE_Read_Timing", "Time since last read: " + delta + " ms");
//                                                    }
//
//                                                    addEntry(chart1, 0, (float) xValueTemp,Float.valueOf(ir)); // IR
//                                                    addEntry(chart1,1, (float) xValueTemp,Float.valueOf(ir)); // IR_ filtere
//                                                    addEntry(chart2,0 , (float) xValueTemp, Float.valueOf(acc));
//
//                                                    lastTimestamp = currentTime;
//
//
//                                                }
//                                                    catch (Exception e){
//                                                        Log.e("BLE_PARSE", "invalid data");
//
//                                                    }
//                                                }
//
//
//
//
//
////                                                int dataChecker = 0;
////                                                try { // Check to see that inputs are valid numbers, otherwise skip the entire packet or else will crash
////                                                    Float.valueOf(sensorData[2]);
////                                                    Float.valueOf(sensorData[3]);
////
////                                                    Float.valueOf(sensorData[4]);
////
////                                                    Float.valueOf(sensorData[5]);
////
////                                                    Float.valueOf(sensorData[6]);
////
////
////
////                                                    dataChecker = 1;
////                                                }
////                                                catch (Exception e) {
////                                                }
////                                                if (dataChecker == 1) { // If floats all converted properly and exist, then add to chart display
////
////                                                    float rawIR = Float.valueOf(sensorData[2]);
////                                                    float rawRED = Float.valueOf(sensorData[3]);
////
////
////
////                                                    float prevIRin = lastIRInput.getOrDefault(2, rawIR);     // fallback: rawIR if missing
////                                                    float prevIRout = lastIROutput.getOrDefault(2, 0f);      // fallback: 0
////
////                                                    float prevREDin = lastREDInput.getOrDefault(3, rawRED);  // fallback: rawRED
////                                                    float prevREDout = lastREDOutput.getOrDefault(3, 0f);    // fallback: 0
////
////
////
////                                                    float filteredIR = alpha * (prevIRout + rawIR - prevIRin);
////                                                    // === Moving Average: IR ===
////
////                                                    if (!irBufferMap.containsKey(2)) {
////                                                        irBufferMap.put(2, new ArrayList<>());
////                                                    }
////                                                    List<Float> irBuffer = irBufferMap.get(2);
////                                                    irBuffer.add(filteredIR);
////
////                                                    if (irBuffer.size() > windowSize * 2) {
////                                                        irBuffer.remove(0); // keep recent values only
////                                                    }
////
//////                                                    if (!irBufferMap.containsKey(2)) irBufferMap.put(2, new ArrayList<>());
//////                                                    irBufferMap.get(2).add(filteredIR);
////
////                                                    float avgIR = calculateMovingAverage(irBufferMap.get(2), windowSize);
////
////
////                                                    lastIRInput.put(2, rawIR);
////                                                    lastIROutput.put(2, avgIR);
////
////
////
////                                                    addEntry(chart1, mymap.get(tempDevice.getMac()), (float) xValueTemp,(float) Float.valueOf(sensorData[2])); // IR
////                                                    addEntry(chart2, mymap.get(tempDevice.getMac()), (float) xValueTemp,(float)(avgIR)); // IR_ filtered
////                                                    addEntry(chart3, mymap.get(tempDevice.getMac()) , (float) xValueTemp,(float) Float.valueOf(sensorData[3])); // RED
////                                                    addEntry(chart4, mymap.get(tempDevice.getMac()) , (float) xValueTemp,(float) Float.valueOf(sensorData[4])); // RED
////                                                    addEntry(chart5, mymap.get(tempDevice.getMac()) , (float) xValueTemp,(float) Float.valueOf(sensorData[5])); // RED
////                                                    addEntry(chart6, mymap.get(tempDevice.getMac()) , (float) xValueTemp,(float) Float.valueOf(sensorData[6])); // RED
//
//                                                    // Add to dataset for eventual excel export
//
//
//                                                }
//
//
//
//
//
//                                            @Override
//                                            public void onReadFailure(final BleException exception) {
//                                                //showToast("Bluetooth unstable.  Move close to device");
//                                                //showToast(String.valueOf(exception));
//                                                //Error message comes here
//                                                Log.e("BLEconnect", "Failed because of: " + exception.toString() + " and "+ exception.getDescription());
//
//                                            }
 //                                       }
  //                              );


//                                // ✅ Nur EINMAL alles aktualisieren:
//                                chart1.getData().notifyDataChanged();
//                                chart1.notifyDataSetChanged();
//                                chart1.invalidate();
//
//                                chart2.getData().notifyDataChanged();
//                                chart2.notifyDataSetChanged();
//                                chart2.invalidate();



                            }
                            }
                        }
                    }
                }
            }
        }
    }



    private void initUI(View v) {
        characteristicList = new ArrayList<>();
        csvData = new ArrayList<>(10);
        mymap = new HashMap<String, Integer>();
        mymapName = new HashMap<Integer, String>();
        dataSetIndex = 0;

        addDevicesReminder = v.findViewById(R.id.addDevicesReminder);
        addDevicesReminder.bringToFront();

        txt_test = v.findViewById(R.id.testText);
        txt_test.setOnClickListener(this);

        btn_record = v.findViewById(R.id.btn_record);
        btn_record.setOnClickListener(this);

        btn_stop = v.findViewById(R.id.btn_save_record);
        btn_stop.setOnClickListener(this);
        btn_stop.setVisibility(v.GONE);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        xValueTemp = 0;
        graphStatus = false;
        displayGraphStatus = false;
        View v = inflater.inflate(R.layout.fragment_graph, container, false);
        initUI(v);

        if (savedInstanceState == null) {
            chart1 = initChart(chart1,v,R.id.chart1, 60f);
            chart2 = initChart(chart2,v,R.id.chart2, 60f);
            chart3 = initChart(chart3,v,R.id.chart3, 60f);
            chart4 = initChart(chart4,v,R.id.chart4, 120f);  // HR chart with range 0-120 BPM

//            chart4 = initChart(chart4,v,R.id.chart4, 60f);
//
//            chart5 = initChart(chart5,v,R.id.chart5, 60f);
//
//            chart6 = initChart(chart6,v,R.id.chart6, 60f);


            rescaleAllCharts();
        }
        processingHandler = new Handler(Looper.getMainLooper());
        processingRunnable = new Runnable() {
            @Override
            public void run() {
                processPpgSignal();
                processingHandler.postDelayed(this, PROCESS_INTERVAL_MS);
            }
        };
        processingHandler.postDelayed(processingRunnable, PROCESS_INTERVAL_MS);

        bpmTextView = v.findViewById(R.id.textview_bpm);

        return v;
    }

    @Override
    public void onDestroyView() {
        processingHandler.removeCallbacks(processingRunnable);
        super.onDestroyView();
    }


    public void resetCharts(View v) {
        chart1 = initChart(chart1,v,R.id.chart1, 60f);
        chart2 = initChart(chart2,v,R.id.chart2, 60f);
        chart3 = initChart(chart3,v,R.id.chart3, 60f);
        chart4 = initChart(chart4,v,R.id.chart4, 120f);  // HR chart with range 0-120 BPM
//        chart4 = initChart(chart4,v,R.id.chart4, 60f);
//
//        chart5 = initChart(chart5,v,R.id.chart5, 60f);
//
//        chart6 = initChart(chart6,v,R.id.chart6, 60f);
//


        rescaleAllCharts();

        xValueTemp = 0;
        graphStatus = false;
        displayGraphStatus = false;
        initUI(getView());
    }

    public void rescaleYAxis(LineChart lc) {
       /*
        LineData data = lc.getData();

        if (data == null) return;

        float max = Float.MIN_VALUE;
        float min = Float.MAX_VALUE;

        for (ILineDataSet set : data.getDataSets()) {
            for (int i = 0; i < set.getEntryCount(); i++) {
                float val = set.getEntryForIndex(i).getY();
                if (val > max) max = val;
                if (val < min) min = val;
            }
        }

        if (min == Float.MAX_VALUE || max == Float.MIN_VALUE) return;

        float range = max - min;
        YAxis leftAxis = lc.getAxisLeft();
        leftAxis.setAxisMinimum(min - 0.2f * range);
        leftAxis.setAxisMaximum(max + 0.2f * range);
        lc.notifyDataSetChanged();
        lc.invalidate();
,

        */

        float minY = Float.MAX_VALUE;
        float maxY = Float.MIN_VALUE;

        YAxis leftAxis = lc.getAxisLeft();
        // Get the current max and min values from the chart
        LineData data = lc.getData();
        List<ILineDataSet> b;
        ILineDataSet a;
        b = data.getDataSets();
        if (b.size()>1)
        {
         a = b.get(1);
        }
        else
        {   a = b.get(0);}

//        List<Entry> copiedEntries = new ArrayList<>(a.getEntryCount());
//        for (int i = 0; i < a.getEntryCount(); i++) {
//            Entry original = a.getEntryForIndex(i);
//            copiedEntries.add(new Entry(original.getX(), original.getY()));
//        }
//
//        int total = copiedEntries.size();
//        int startIndex = Math.max(0, total - 50);
//
//
//
//        for (int i = startIndex; i < total; i++) {
//            float y = copiedEntries.get(i).getY();
//            if (y < minY) minY = y;
//            if (y > maxY) maxY = y;
//        }




        minY = a.getYMin();
        maxY = a.getYMax();
//

/*
        float minY = Float.MAX_VALUE;
        float maxY = Float.MIN_VALUE;

        if (a.getEntryCount()>0)
        {
            int n = 200;
            int entryCount = a.getEntryCount();

            for (int i = Math.max(0, entryCount - n); i < entryCount; i++) {
                float y = a.getEntryForIndex(i).getY();
                if (!Float.isNaN(y) && !Float.isInfinite(y)) {
                    maxY = Math.max(maxY, y);
                    minY = Math.min(minY, y);
                }
            }
        }

 */


        float newMax = maxY + 0.05f * (maxY - minY);
        float newMin = minY - 0.01f * (maxY - minY);

        // Apply new scale
//        leftAxis.setAxisMaximum(maxY);
//        leftAxis.setAxisMinimum(minY);

        leftAxis.setAxisMaximum(newMax);
        leftAxis.setAxisMinimum(newMin);

        // Refresh the chart to apply changes
        lc.invalidate();
    }

    public void rescaleAllCharts() {
        rescaleYAxis(chart1);
        rescaleYAxis(chart2);
        rescaleYAxis(chart3);
        rescaleYAxis(chart4);
//        rescaleYAxis(chart4);
//
//        rescaleYAxis(chart5);
//
//        rescaleYAxis(chart6);



    }

    private void addEntry(LineChart chartTemp, int dataSetIndex, float xVal, float yVal) {
        LineData data = chartTemp.getData();
        if (data != null) {
            ILineDataSet set = data.getDataSetByIndex(dataSetIndex);
            if (set == null) {
                set = createSet();
                data.addDataSet(set);
            }

            if (set.getEntryCount() >= 100) {
                // Remove the oldest entry (index 0)
                set.removeFirst();
            }



            //data.addEntry(new Entry(set.getEntryCount(), yVal),dataSetIndex);
            data.addEntry(new Entry(xVal, yVal),dataSetIndex);

//            if (chartUpdateCounter == 100) {
//                chartUpdateCounter=0;
//                chartTemp.notifyDataSetChanged();
//                chartTemp.invalidate();
//                data.notifyDataChanged();
//                chartTemp.setVisibleXRangeMaximum(5);
//
//            }

            chartTemp.notifyDataSetChanged();
            chartTemp.invalidate();
            data.notifyDataChanged();
            chartTemp.setVisibleXRangeMaximum(5);
//           chartTemp.notifyDataSetChanged();
            chartTemp.moveViewTo(xValueTemp-5, 01f, YAxis.AxisDependency.LEFT);
        }
    }

    private void addEntryToDataSet(LineChart chart, int dataSetIndex, float x, float y) {
        ILineDataSet dataSet = chart.getData().getDataSetByIndex(dataSetIndex);
        if (dataSet != null) {
            dataSet.addEntry(new Entry(x, y));
        }
    }

    private LineDataSet createSet() {
        LineDataSet set = new LineDataSet(null, "Dynamic Data");
        set.setAxisDependency(YAxis.AxisDependency.LEFT);
        set.setHighLightColor(Color.rgb(0,0,0));
        set.setColor(ColorTemplate.getHoloBlue());
        set.setCircleColor(Color.WHITE);
        set.setLineWidth(4f);
        set.setCircleRadius(4f);
        set.setFillAlpha(30);
        set.setFillColor(ColorTemplate.getHoloBlue());
        set.setHighLightColor(Color.rgb(244, 117, 117));
        set.setValueTextColor(Color.WHITE);
        set.setValueTextSize(9f);
        set.setDrawValues(false);
        return set;
    }

    private void Chart(LineChart chartTemp, View viewTemp, int viewID) {
        // Find Chart by ID
        chartTemp = (LineChart) viewTemp.findViewById(viewID);
        LineData data = chartTemp.getData();
    }

    private LineChart initChart(LineChart chartTemp, View viewTemp,int viewID, float rangeApprox) {
        // Find Chart by ID
        chartTemp = (LineChart) viewTemp.findViewById(viewID);

        // Formatting
        chartTemp.getDescription().setEnabled(true);
        chartTemp.getDescription().setText("");
        chartTemp.setDragEnabled(true);
        chartTemp.setScaleEnabled(true);
        chartTemp.setPinchZoom(true);
        chartTemp.getXAxis().setPosition(XAxis.XAxisPosition.BOTTOM);
        chartTemp.getAxisLeft().setTextColor(R.color.colorTextPrimary);
        chartTemp.getAxisRight().setTextColor(R.color.colorTextPrimary);
        chartTemp.getXAxis().setTextColor(R.color.colorTextPrimary);
        chartTemp.getXAxis().setTextSize(10);
        chartTemp.getAxisLeft().setTextSize(10);
        chartTemp.getAxisRight().setEnabled(false);
        chartTemp.getLegend().setTextColor(R.color.colorTextPrimary);
        chartTemp.getLegend().setForm(Legend.LegendForm.CIRCLE);
        chartTemp.setVisibleXRangeMaximum(3);



        final LineChart finalChartTemp = chartTemp;
        chartTemp.setOnChartValueSelectedListener(new OnChartValueSelectedListener()
        {
            @Override
            public void onValueSelected(Entry e, Highlight h)
            {
                float x=e.getX();
                float y=e.getY();
                finalChartTemp.getDescription().setText("x="+Float.toString(x) + ",y=" + Float.toString(y));
            }

            @Override
            public void onNothingSelected()
            {
                finalChartTemp.getDescription().setText("");
            }
        });

        Legend legend = chartTemp.getLegend();
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.TOP);
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.LEFT);
        legend.setOrientation(Legend.LegendOrientation.HORIZONTAL);
        legend.setDrawInside(false);

        // First Data Set

        int randColor = getRandomColor();
        ArrayList<Entry> yValues = new ArrayList<>();
        yValues.add(new Entry(0, 0));
        LineDataSet set1 = new LineDataSet(yValues,"");
        set1.setHighLightColor(Color.rgb(0,0,0));
        set1.setCircleRadius(1);
        set1.setFillColor(Color.rgb(250,250,250));
        set1.setCircleColor(Color.rgb(250,250,250));
        set1.setColor(Color.rgb(250,250,250));
        set1.setDrawValues(false);
        ArrayList<ILineDataSet> dataSets = new ArrayList<>();
        dataSets.add(set1);

        LineData data = new LineData(dataSets);
        chartTemp.setData(data);

        return chartTemp;
    }

    private LineDataSet initializeLineDataSet(String setName, int randomColor) {
        ArrayList<Entry> yValues = new ArrayList<>();
        LineDataSet setTemp = new LineDataSet(yValues,setName);
        setTemp.setHighLightColor(Color.rgb(0,0,0));
        setTemp.setCircleRadius(1);

        setTemp.setFillColor(Color.RED);

        setTemp.setCircleColor(randomColor);
        setTemp.setColor(randomColor);

        setTemp.setLineWidth(2f);

        setTemp.setFillAlpha(50);

        setTemp.setDrawFilled(true);
        setTemp.setDrawValues(false);

        setTemp.setMode(LineDataSet.Mode.LINEAR);

        return setTemp;
    }

    final Random mRandom = new Random(System.currentTimeMillis());

    public int getRandomColor() {
        Random rand = new Random();
        int r = rand.nextInt(6); //increase number to add more colors

        int[] randomColor = new int[10];
        randomColor[0] = Color.rgb(244, 67, 54);
        randomColor[1] = Color.rgb(236, 64, 122);
//        randomColor[2] = Color.rgb(31, 35, 194);
//        randomColor[3] = Color.rgb(30, 196, 181);
//        randomColor[4] = Color.rgb(85, 230, 98);
//        randomColor[5] = Color.rgb(85, 230, 98);
        //TODO: add rgb values for random colors ;)

        r = r - 1;
        if (r <= 0) {r = 0;}
        if(r > 5) r = 5;
        return randomColor[r];
    }


    private void showToast (String msg) {
        Toast.makeText(getActivity(), msg, Toast.LENGTH_SHORT).show();
    }

    private void showToast (String msg, Context context) {
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show();
    }

    // BLE DECODER
    private static String hexToAscii(String hexStr) {
        StringBuilder output = new StringBuilder("");

        for (int i = 0; i < hexStr.length(); i += 2) {
            String str = hexStr.substring(i, i + 2);
            output.append((char) Integer.parseInt(str, 16));
        }

        return output.toString();
    }

    private static String getStringRepresentation(ArrayList<Character> list)
    {
        StringBuilder builder = new StringBuilder(list.size());
        for(Character ch: list)
        {
            builder.append(ch);
        }
        return builder.toString();
    }

//    private static String[] unpackageBLEPacket(String packetData)
//    {
//        String[] sensorData;
//        try {
//            sensorData = packetData.split("/");
//            for (int i = 0; i < sensorData.length; i++) {
//                sensorData[i] = sensorData[i].substring(1);
//            }
//        }
//        catch (Exception ee) {
//            String[] errorHolder = new String[2];
//            errorHolder[0] = "Array";
//            errorHolder[1] = "Failed";
//            sensorData = errorHolder;
//        }
//        return sensorData;
//    }

    private static String[][] unpackageBLEPacketMulti(String packetData) {
        String[][] parsedSamples;

        try {
            // 1. Aufteilen in einzelne Pakete (beginnt immer mit 'B...')
            String[] packets = packetData.split("(?=B)");

            parsedSamples = new String[packets.length][];
            for (int i = 0; i < packets.length; i++) {
                String[] fields = packets[i].split("/");

                // Nur wenn mindestens 6 Werte vorhanden sind (B/C/D/E/F/G)
                if (fields.length >= 6) {
                    String[] oneSample = new String[6];  // B, C, D, E, F, G

                    for (int j = 0; j < 6; j++) {
                        // Nur wenn das Feld lang genug ist
                        if (fields[j].length() > 1) {
                            oneSample[j] = fields[j].substring(1);  // entferne Buchstabenpräfix
                        } else {
                            oneSample[j] = "0";  // Fallback
                        }
                    }
                    parsedSamples[i] = oneSample;
                } else {
                    // Ungültiges Paket – fülle mit Nullen
                    parsedSamples[i] = new String[]{"0", "0", "0", "0", "0", "0"};
                }
            }

        } catch (Exception e) {
            // Fehlerbehandlung – gib nur ein Sample mit Fehlermeldung zurück
            parsedSamples = new String[1][2];
            parsedSamples[0][0] = "Array";
            parsedSamples[0][1] = "Failed";
        }

        return parsedSamples;
    }




    private Thread thread;

    public void startGraphing() {
        if (thread != null) {thread.interrupt();}
        final Runnable runnable = new Runnable() {
            int counter = 0;

            @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
            @Override
            public void run() {
                if (graphStatus) {
                    getData();
                    //Call for sudden disconnection
                    if(!((MainActivity)getActivity()).getIsConnected()){
                        //showToast("Sudden Disconnection, data is saved");
                        btn_record.setBackgroundResource(R.drawable.ic_baseline_fiber_manual_record_24);
                        btn_stop.setVisibility(getView().GONE);
                        suddenDisconnectAlert();
                        graphStatus = false;
                        addDevicesReminder.setVisibility(View.INVISIBLE);
                        addDevicesReminder.setVisibility(View.VISIBLE);
                    }
                }
                if (counter < 10 || counter%100 == 0) {
                    rescaleAllCharts();
                }
                counter=counter + 1;
            }
        };
        thread = new Thread(new Runnable() {
            @Override
            public void run() {
                while (graphStatus) {
                    getActivity().runOnUiThread(runnable);
                    try {
                        // every one second a new sample of data
                        Thread.sleep(50);
                     //   xValueTemp = xValueTemp + (float) 0.01;
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        Log.e(TAG, e.getMessage());
                    }
                }
            }
        });
        thread.start();
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_record:
                if (graphStatus) {
                    // DO THIS WHEN RECORDING
                    btn_record.setBackgroundResource(R.drawable.ic_baseline_fiber_manual_record_24);
                    graphStatus = false;
                }
                else {
                    // EXIT RECORD
                    if( ((MainActivity)getActivity()).getIsConnected()) {
                        if(((MainActivity) getActivity()).getIsGraphing()) {
                            graphStatus = true;
                            startGraphing();
                            btn_stop.setVisibility(getView().VISIBLE);
                            btn_record.setBackgroundResource(R.drawable.ic_baseline_pause_24);
                        }
                        else{
                            showToast("Graphing is not activated");
                        }
                    }
                    else {
                        showToast("No device connected");
                    }
                }
                break;
            case R.id.btn_save_record:
                btn_record.setBackgroundResource(R.drawable.ic_baseline_fiber_manual_record_24);
                btn_stop.setVisibility(getView().GONE);
                //exportData(getView());
                saveDataToCSV();
                graphStatus = false;
                addDevicesReminder.setVisibility(View.INVISIBLE);
                break;
        }
    }

    private void suddenDisconnectAlert(){
        new AlertDialog.Builder(getContext())
                .setTitle("Sudden Disconnection to BLE device")
                .setMessage("Recorded data can be saved")
                .setPositiveButton("Confirm", (dialog, which) -> {
                    saveDataToCSV();
                })
                .setCancelable(false) // Prevent back button or outside touch to cancel
                .show();
    }

    public void saveDataToCSV() {
        AlertDialog.Builder alert = new AlertDialog.Builder(getActivity());
        alert.setTitle("File Name");
        alert.setMessage("Save file as:");
        // Set an EditText view to get user input
        final EditText input = new EditText(getActivity());

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd(H_mm_ss)", Locale.US);
        Date now = new Date();
        final String fileName = formatter.format(now);

        input.setText(fileName);
        alert.setView(input);

        alert.setPositiveButton("Ok", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int whichButton) {
                Editable value = input.getText();

                //showToast(value.toString());
                // ADD CODE AFTER CONFIRMATION OF FILE SAVE
                exportCSV(getView(), fileName,data_txt);
                resetCharts(getView());
            }
        });
        alert.setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int whichButton) {
                AlertDialog.Builder cancelAlert = new AlertDialog.Builder(getActivity());
                cancelAlert.setTitle("Confirm data deletion");
                cancelAlert.setMessage("Are you sure you don't want to save?");
                cancelAlert.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int whichButton) {
                        resetCharts(getView());
                    }
                });
                cancelAlert.setNegativeButton("No", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int whichButton) {
                        saveDataToCSV();
                    }
                });
                cancelAlert.show();
            }
        });
        alert.show();
    }

//    private void filteredData(LineChart chartTemp, int windowSize) {
//        LineData data = chartTemp.getData();
//        List<Entry> smoothedEntries = new ArrayList<>();
//
//        ILineDataSet set = data.getDataSetByIndex(dataSetIndex);
//        if (set != null & set.getEntryCount() < windowSize){
//        for (int i = 0; i < set.getEntryCount(); i++) {
//            float sum = 0;
//            int count = 0;
//
//            // Compute the moving average window
//            for (int j = i - windowSize / 2; j <= i + windowSize / 2; j++) {
//                if (j >= 0 && j < set.getEntryCount()) {
//                    sum += set.getEntryForIndex(j).getY();
//                    count++;
//                }
//            }
//
//            float avg = sum / count;
//            Entry original = set.getEntryForIndex(i);
//            smoothedEntries.add(new Entry(original.getX(), avg));
//        }
//            set.clear();
//            for (Entry e : smoothedEntries) {
//                set.addEntry(e);
//            }
//
//            data.notifyDataChanged();
//            chartTemp.notifyDataSetChanged();
//            chartTemp.invalidate();
//
//    }
//    }


    private float applyHighPassFilter(float currentInput, float lastInput, float lastOutput, float alpha) {

        return alpha * (lastOutput + currentInput - lastInput);

    }



    private float calculateMovingAverage(List<Float> values, int windowSize) {

        int size = values.size();

        if (size < windowSize) return values.get(size - 1);  // not enough values yet



        float sum = 0;

        for (int i = size - windowSize; i < size; i++) {

            sum += values.get(i);

        }

        return sum / windowSize;

    }
}


