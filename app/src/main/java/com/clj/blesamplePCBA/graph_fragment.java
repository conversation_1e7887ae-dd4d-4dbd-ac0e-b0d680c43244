package com.clj.blesample<PERSON><PERSON>;

import android.Manifest;
import android.app.AlertDialog;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattService;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;

import android.provider.MediaStore;
import android.util.Log;

import android.text.Editable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.clj.blesamplePCBA.adapter.DeviceAdapter;
import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleNotifyCallback;
import com.clj.fastble.callback.BleReadCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.listener.OnChartValueSelectedListener;
import com.github.mikephil.charting.utils.ColorTemplate;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
//SPINNER
import androidx.annotation.RequiresApi;
import androidx.annotation.RequiresPermission;
import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;

public class graph_fragment extends Fragment implements View.OnClickListener {

    private static final String TAG = "graph_fragment TAG";
    private static final int LONG_DELAY = 3500; // 3.5 seconds
    private static final int SHORT_DELAY = 2000; // 2 seconds
    public final static UUID UUID_SERVICE = UUID.fromString("4fafc201-1fb5-459e-8fcc-c5c9c331914b");
    public final static UUID UUID_CHARACTERISTIC = UUID.fromString("beb5483e-36e1-4688-b7f5-ea07361b26a8");
    private boolean graphStatus;
    private boolean displayGraphStatus;

    private float xValueTemp;
    private float previousTime;
    private float currentTime;
    private int dataSetIndex;

    private LineChart chart1; // Temp
    private LineChart chart2; // Humidity
    private LineChart chart3; // PPM
    private LineChart chart4; // R1
    private LineChart chart5; // dR1
    private LineChart chart6; // R2
    private LineChart chart7; // dR2
    private LineChart chart8; // R3
    private LineChart chart9; // dR3
    private LineChart chart10; // R4
    private LineChart chart11; // dR4

    // Store all data into 3D arraylist
    ArrayList<ArrayList<ArrayList<String>>> csvData;

    private TextView txt_test;
    private Button btn_record;
    private RelativeLayout addDevicesReminder;
    private Button btn_stop;

    private DeviceAdapter mDeviceAdapter;
    private DeviceAdapter connectedDeviceAdapter;
    private List<BluetoothGattService> serviceList;
    private List<BluetoothGattCharacteristic> characteristicList;

    List<BleDevice> connectedDevices;
    Map<String, Integer> mymap;
    Map<Integer, String> mymapName;


    // filter parameters and necessary classes

    float alpha = 0.927f; // tuned for 0.3Hz cutoff with 25ms sample rate

    int windowSize = 5;
    int chartUpdateCounter=0;


    private Map<Integer, Float> lastIRInput = new HashMap<>();

    private Map<Integer, Float> lastIROutput = new HashMap<>();

    private Map<Integer, Float> lastREDInput = new HashMap<>();

    private Map<Integer, Float> lastREDOutput = new HashMap<>();

    private Map<Integer, List<Float>> irBufferMap = new HashMap<>();



    // list of data samples received from the buffer and use to update the graphs


    List<Entry> irEntries = new ArrayList<>();
    List<Entry> redEntries = new ArrayList<>();
    List<Entry> accEntries = new ArrayList<>();

    boolean textFileInit = false;
    long lastTimestamp = -1;

    ArrayList<ArrayList<String>> data_txt = new ArrayList<>();



    ArrayList<String> header = new ArrayList<>();



    public void exportCSV(View v, String txtFileName, ArrayList<ArrayList<String>> data_txt) {
        try {
            Context context = getActivity().getApplicationContext();
            String path = "";

            // Create target folder
            File documents = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
            final String folderName = "ArduNet_TXT_measurements";
            File folder = new File(documents, folderName);
            if (!folder.exists()) {
                boolean created = folder.mkdirs();
                Log.d("Storage", "Folder created: " + created);
            }

            // Convert data to plain text format
            StringBuilder data = new StringBuilder();
            for (ArrayList<String> row : data_txt) {
                for (int i = 0; i < row.size(); i++) {
                    data.append(row.get(i));
                    if (i < row.size() - 1) {
                        data.append("\t"); // tab-separated for text file
                    }
                }
                data.append("\n");
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                ContentValues values = new ContentValues();
                values.put(MediaStore.Downloads.DISPLAY_NAME, txtFileName + ".txt");
                values.put(MediaStore.Downloads.MIME_TYPE, "text/plain");
                values.put(MediaStore.Downloads.IS_PENDING, 1);
                values.put(MediaStore.Downloads.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS + "/" + folderName);

                ContentResolver resolver = context.getContentResolver();
                Uri collection = MediaStore.Downloads.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY);
                Uri fileUri = resolver.insert(collection, values);
                path = fileUri.toString();

                try (OutputStream out = resolver.openOutputStream(fileUri)) {
                    out.write(data.toString().getBytes());
                    out.flush();
                    Toast.makeText(context, "Saved to Downloads/" + folderName, Toast.LENGTH_SHORT).show();
                }

                values.clear();
                values.put(MediaStore.Downloads.IS_PENDING, 0);
                resolver.update(fileUri, values, null, null);
            } else {
                File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
                File fullDir = new File(downloadsDir, folderName);
                File file = new File(fullDir, txtFileName + ".txt");
                path = file.getAbsolutePath();

                try (FileOutputStream out = new FileOutputStream(file)) {
                    out.write(data.toString().getBytes());
                    Toast.makeText(context, "Saved to Downloads/" + folderName, Toast.LENGTH_SHORT).show();
                }
            }

            // Optional: share the file
            Intent fileIntent = new Intent(Intent.ACTION_SEND);
            fileIntent.setType("text/plain");
            fileIntent.putExtra(Intent.EXTRA_SUBJECT, txtFileName);
            fileIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            if (!path.equals("")) {
                fileIntent.putExtra(Intent.EXTRA_STREAM, Uri.parse(path));
            }
            startActivity(Intent.createChooser(fileIntent, "Send file"));

        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(getActivity(), "Error: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    boolean notificationEnabled = false;

//    public void exportCSV(View v, String csvDefaultName) {
//        for (int sheetID = 0; sheetID < csvData.size(); sheetID++) {
//            String csvName = mymapName.get(sheetID + 1) + "_" + csvDefaultName;
//            StringBuilder data = new StringBuilder();
//            for (int rowID = 0; rowID < csvData.get(sheetID).size(); rowID++) {
//                for (int columnID = 0; columnID < csvData.get(sheetID).get(rowID).size(); columnID++) {
//                    if (columnID == 0) {
//                        data.append(csvData.get(sheetID).get(rowID).get(columnID));
//                    } else {
//                        data.append("," + csvData.get(sheetID).get(rowID).get(columnID));
//                    }
//                }
//                data.append("\n");
//            }
//            try{
//
//                //old implementation
//                /*
//                Context context = getActivity().getApplicationContext();
//                try (FileOutputStream out = context.openFileOutput(csvName+".csv", Context.MODE_PRIVATE)){
//                    out.write((data.toString()).getBytes());
//                }
//                 */
//
//                Context context = getActivity().getApplicationContext();
//                String path = "";
//
//                //create Folder in Documents Folder
//                File documents = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
//                final String folderName = "ArduNet_CSV_measurements";
//                File folder = new File(documents, folderName);
//                if (!folder.exists()) {
//                    boolean created = folder.mkdirs();
//                    Log.d("Storage", "Public folder created: " + created);
//                }
//
//                //Depending on Android version, data needs to be stored in two methods
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
//                    //For Android 10+
//                    //MediaStore was introduced in Android 10+
//                    ContentValues values = new ContentValues(); //File saver object
//                    values.put(MediaStore.Downloads.DISPLAY_NAME, csvName + ".csv");
//                    values.put(MediaStore.Downloads.MIME_TYPE, "text/csv");
//                    values.put(MediaStore.Downloads.IS_PENDING, 1);
//                    values.put(MediaStore.Downloads.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS + "/" + folderName);
//
//                    ContentResolver resolver = getContext().getContentResolver();
//                    Uri collection = MediaStore.Downloads.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY);
//                    Uri fileUri = resolver.insert(collection, values);
//                    path = fileUri.toString();
//
//                    //write data
//                    try (OutputStream out = resolver.openOutputStream(fileUri)) {
//                        out.write(data.toString().getBytes());
//                        out.flush();
//                        Toast.makeText(context, "File saved to Downloads/" + folderName, Toast.LENGTH_SHORT).show();
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//
//                    // Make file visible
//                    values.clear();
//                    values.put(MediaStore.Downloads.IS_PENDING, 0);
//                    resolver.update(fileUri, values, null, null);
//                } else {
//                    //For Android 9 and below
//                    if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
//                        File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
//                        File fullDir = new File(downloadsDir, folderName);
//                        File file = new File(fullDir, csvName + ".csv");
//                        path = file.getAbsolutePath();
//
//                        try (FileOutputStream out = new FileOutputStream(file)){
//                            out.write((data.toString()).getBytes());
//                            Toast.makeText(context, "File saved to Downloads/" + folderName, Toast.LENGTH_SHORT).show();
//                        }
//                        catch (IOException e) {
//                            e.printStackTrace();
//                            Toast.makeText(context, "File save failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
//                        }
//                    }
//                }
//
//                //exporting
//                Intent fileIntent = new Intent(Intent.ACTION_SEND);
//                fileIntent.setType("text/csv");
//                fileIntent.putExtra(Intent.EXTRA_SUBJECT, csvName); // File Name
//                fileIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
//                if(!path.equals("")) {
//                    fileIntent.putExtra(Intent.EXTRA_STREAM, path);
//                }
//                else{
//                    Log.e("File Exporting", "path not set correctly");
//                }
//                startActivity(Intent.createChooser(fileIntent, "Send mail"));
//            }
//            catch(Exception e){
//                e.printStackTrace();
//                Toast.makeText(getActivity(), "Error saving file: " + e.getMessage(), Toast.LENGTH_LONG).show();
//            }
//        }
//    }


    @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
    private void getData() {
        connectedDeviceAdapter = ((MainActivity)getActivity()).getDeviceAdapter();
        for (int ii = 0; ii < connectedDeviceAdapter.getCount();ii++) {
            final BleDevice tempDevice = connectedDeviceAdapter.getItem(ii); // Get BLE devices from connected list
            if (tempDevice.getGraphStatus() == 1) { // Filter by graph status
                serviceList = BleManager.getInstance().getBluetoothGattServices(tempDevice);
                for (BluetoothGattService gattService:serviceList) { // Parse all services
                    characteristicList = BleManager.getInstance().getBluetoothGattCharacteristics(gattService);
                    if (gattService.getUuid().toString().equalsIgnoreCase(UUID_SERVICE.toString())) { // Filter by UUID
                        for (BluetoothGattCharacteristic gattCharacteristic : characteristicList) { // Parse all characteristics
                            if (gattCharacteristic.getUuid().toString().equalsIgnoreCase(UUID_CHARACTERISTIC.toString())) {
                                if (!notificationEnabled){
                                BleManager.getInstance().notify(
                                        tempDevice,
                                        gattService.getUuid().toString(),
                                        gattCharacteristic.getUuid().toString(),
                                        new BleNotifyCallback() {
                                            @Override
                                            public void onNotifySuccess() {
                                                Log.d("BLE_NOTIFY", "Notification enabled");
                                                notificationEnabled = true;
                                            }

                                            @Override
                                            public void onNotifyFailure(BleException exception) {
                                                Log.e("BLE_NOTIFY", "Failed to enable notification: " + exception.toString());
                                            }

                                            @Override
                                            public void onCharacteristicChanged(byte[] data) {
                                                // ⚡ This is where your data arrives
                                         //       String rawData = Arrays.toString(data);
                                          //      Log.d("BLE_NOTIFY", "Received data: " + rawData);

                                                try {
                                                    if (!textFileInit) {
                                                        textFileInit = true;
                                                        header.add("Time Difference");
                                                        header.add("IR");
                                                        header.add("RED");
                                                        header.add("ACC");
                                                        data_txt.add(header);
                                                    }

                                                    if (mymap.get(tempDevice.getMac()) == null) {
                                                        addDevicesReminder.setVisibility(View.GONE);
                                                        ILineDataSet tempDataSet = initializeLineDataSet("IR", Color.BLUE);
                                                        chart1.getData().addDataSet(tempDataSet);
                                                        chart2.getData().addDataSet(initializeLineDataSet("RED", Color.RED));
                                                        chart3.getData().addDataSet(initializeLineDataSet("ACC",Color.GREEN));

                                                        dataSetIndex = chart1.getData().getDataSetCount()-1;
                                                        mymap.put(tempDevice.getMac(),dataSetIndex);
                                                        mymapName.put(dataSetIndex,tempDevice.getName());
//                                                        ArrayList<ArrayList<String>> newSheet = new ArrayList<>();
//                                                        ArrayList<String> headers = new ArrayList<>(Arrays.asList("Date", "Time", "IR", "IR filtered", "RED", "ACC x", "ACC y", "ACC z"));
//                                                        newSheet.add(headers);
//                                                        csvData.add(dataSetIndex - 1, newSheet);
                                                    } else {
                                                        dataSetIndex = mymap.get(tempDevice.getMac());
                                                    }

                                                    int sampleSize = 7;
                                                    int sampleCount = data.length / sampleSize;

                                                    for (int i = 0; i < sampleCount; i++) {
                                                        int offset = i * sampleSize;

                                                        int ir = ((data[offset] & 0xFF) << 8) | (data[offset + 1] & 0xFF);
                                                        int red = ((data[offset + 2] & 0xFF) << 8) | (data[offset + 3] & 0xFF);
                                                    //    int acc = (short) (((data[offset + 4] & 0xFF) << 8) | (data[offset + 5] & 0xFF));
                                                        int acc = (((data[offset + 4] & 0xFF) << 8) | (data[offset + 5] & 0xFF));


//                                                        long timestamp = ((data[offset + 6] & 0xFFL) << 24) |
//                                                                ((data[offset + 7] & 0xFFL) << 16) |
//                                                                ((data[offset + 8] & 0xFFL) << 8) |
//                                                                ((data[offset + 9] & 0xFFL));

                                                        long difference = (((data[offset + 6] & 0xFF)));


                                                        //   long timestamp = System.currentTimeMillis();
                                                        ArrayList<String> row = new ArrayList<>();
                                                        row.add(String.valueOf(difference));
                                                        row.add(String.valueOf(ir));
                                                        row.add(String.valueOf(red));
                                                        row.add(String.valueOf(acc));
                                                        data_txt.add(row);

                                                        xValueTemp += 0.05f;

                                                        long currentTime = System.currentTimeMillis();
                                                        if (lastTimestamp != -1) {
                                                            long delta = currentTime - lastTimestamp;
                                                            Log.d("BLE_Read_Timing", "Time since last read: " + delta + " ms");
                                                        }

                                                        addEntry(chart1, mymap.get(tempDevice.getMac()), xValueTemp, (float) ir);
                                                        addEntry(chart2, mymap.get(tempDevice.getMac()), xValueTemp, (float) red); // filtered or placeholder
                                                        addEntry(chart3, mymap.get(tempDevice.getMac()), xValueTemp, (float) acc);

                                                        lastTimestamp = currentTime;


//                                                        try {
//                                                            Thread.sleep(20);  // This will block the UI thread if used in main thread!
//                                                        } catch (InterruptedException e) {
//                                                            e.printStackTrace();
//                                                        }
                                                    }

                                                } catch (Exception e) {
                                                    Log.e("BLE_NOTIFY_PARSE", "Failed to parse notification data: " + e.getMessage());
                                                }                                            }
                                        }
                                );





                                // Filter by UUID
//                                BleManager.getInstance().read(
//                                        tempDevice,
//                                        gattService.getUuid().toString(),
//                                        gattCharacteristic.getUuid().toString(),
//                                        new BleReadCallback() { // Get broadcast
//                                            @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
//                                            @RequiresApi(api = Build.VERSION_CODES.KITKAT)
//                                            @Override



//                                            public void onReadSuccess(final byte[] data) {
//
//                                                if(!textFileInit)
//                                                {
//                                                    textFileInit = true;
//                                                    header.add("Timestamp");
//                                                    header.add("IR");
//                                                    header.add("RED");
//                                                    header.add("ACC");
//                                                    data_txt.add(header);
//                                                }
//
//                                                if (!mymap.containsKey(tempDevice.getMac())) {
//                                                    addDevicesReminder.setVisibility(View.GONE);
//
//                                                    ILineDataSet tempDataSet = initializeLineDataSet(tempDevice.getName(), getRandomColor());
//                                                    chart1.getData().addDataSet(tempDataSet);
//                                                    chart2.getData().addDataSet(initializeLineDataSet(tempDevice.getName(), getRandomColor()));
//
//                                                    dataSetIndex = chart1.getData().getDataSetCount() - 1;
//                                                    mymap.put(tempDevice.getMac(), dataSetIndex);
//                                                    mymapName.put(dataSetIndex, tempDevice.getName());
//
//                                                    ArrayList<ArrayList<String>> newSheet = new ArrayList<>();
//                                                    ArrayList<String> headers = new ArrayList<>(Arrays.asList("Date", "Time", "IR", "IR filtered", "RED", "ACC x", "ACC y", "ACC z"));
//                                                    newSheet.add(headers);
//                                                    csvData.add(dataSetIndex - 1, newSheet);
//                                                } else {
//                                                    dataSetIndex = mymap.get(tempDevice.getMac());
//                                                }
//
//                                                //txt_test.setText(txt_test.getText() + tempDevice.getMac());
//
//                                                //String str = new String(data, StandardCharsets.UTF_8);
//
//                                               // String[][] allSamples = unpackageBLEPacketMulti(str);
//
//                                                // Each sample is 10 bytes: [IR(2), RED(2), AX(2), AY(2), AZ(2)]
//                                                int sampleSize = 50;
//                                                int sampleCount = data.length / sampleSize;
//
//                                                for (int i = 0; i < sampleCount; i++) {
//                                                    try{
//                                                    int offset = i * sampleSize;
//
//                                                    int ir = ((data[offset] & 0xFF) << 8) | (data[offset + 1] & 0xFF);
//                                                    int red = ((data[offset + 2] & 0xFF) << 8) | (data[offset + 3] & 0xFF);
//                                                    int acc = (short)(((data[offset + 4] & 0xFF) << 8) | (data[offset + 5] & 0xFF));
////                                                    int ay = (short)(((data[offset + 6] & 0xFF) << 8) | (data[offset + 7] & 0xFF));
////                                                    int az = (short)(((data[offset + 8] & 0xFF) << 8) | (data[offset + 9] & 0xFF));
//
//                                                    long timestamp = System.currentTimeMillis();
//
//                                                    ArrayList<String> row = new ArrayList<>();
//                                                    row.add(String.valueOf(timestamp));
//                                                    row.add(String.valueOf(ir));
//                                                    row.add(String.valueOf(red));
//                                                    row.add(String.valueOf(acc));
//
//                                                   //     row.add(String.valueOf(acc));
////                                                    row.add(String.valueOf(ay));
////                                                    row.add(String.valueOf(az));
//
//                                                    data_txt.add(row);
//
//                                                    // Optional: You can also plot here if needed
//                                                    // float accMag = (float)Math.sqrt(ax * ax + ay * ay + az * az);
//                                                    // addEntry(chart, datasetIndex, xValue, accMag);
//                                                    xValueTemp += 0.02f;
//
//
//                                                    long currentTime = System.currentTimeMillis(); // or use timestamp from sensor data if embedded
//
//                                                    if (lastTimestamp != -1) {
//                                                        long delta = currentTime - lastTimestamp;
//                                                        Log.d("BLE_Read_Timing", "Time since last read: " + delta + " ms");
//                                                    }
//
//                                                    addEntry(chart1, 0, (float) xValueTemp,Float.valueOf(ir)); // IR
//                                                    addEntry(chart1,1, (float) xValueTemp,Float.valueOf(ir)); // IR_ filtere
//                                                    addEntry(chart2,0 , (float) xValueTemp, Float.valueOf(acc));
//
//                                                    lastTimestamp = currentTime;
//
//
//                                                }
//                                                    catch (Exception e){
//                                                        Log.e("BLE_PARSE", "invalid data");
//
//                                                    }
//                                                }
//
//
//
//
//
////                                                int dataChecker = 0;
////                                                try { // Check to see that inputs are valid numbers, otherwise skip the entire packet or else will crash
////                                                    Float.valueOf(sensorData[2]);
////                                                    Float.valueOf(sensorData[3]);
////
////                                                    Float.valueOf(sensorData[4]);
////
////                                                    Float.valueOf(sensorData[5]);
////
////                                                    Float.valueOf(sensorData[6]);
////
////
////
////                                                    dataChecker = 1;
////                                                }
////                                                catch (Exception e) {
////                                                }
////                                                if (dataChecker == 1) { // If floats all converted properly and exist, then add to chart display
////
////                                                    float rawIR = Float.valueOf(sensorData[2]);
////                                                    float rawRED = Float.valueOf(sensorData[3]);
////
////
////
////                                                    float prevIRin = lastIRInput.getOrDefault(2, rawIR);     // fallback: rawIR if missing
////                                                    float prevIRout = lastIROutput.getOrDefault(2, 0f);      // fallback: 0
////
////                                                    float prevREDin = lastREDInput.getOrDefault(3, rawRED);  // fallback: rawRED
////                                                    float prevREDout = lastREDOutput.getOrDefault(3, 0f);    // fallback: 0
////
////
////
////                                                    float filteredIR = alpha * (prevIRout + rawIR - prevIRin);
////                                                    // === Moving Average: IR ===
////
////                                                    if (!irBufferMap.containsKey(2)) {
////                                                        irBufferMap.put(2, new ArrayList<>());
////                                                    }
////                                                    List<Float> irBuffer = irBufferMap.get(2);
////                                                    irBuffer.add(filteredIR);
////
////                                                    if (irBuffer.size() > windowSize * 2) {
////                                                        irBuffer.remove(0); // keep recent values only
////                                                    }
////
//////                                                    if (!irBufferMap.containsKey(2)) irBufferMap.put(2, new ArrayList<>());
//////                                                    irBufferMap.get(2).add(filteredIR);
////
////                                                    float avgIR = calculateMovingAverage(irBufferMap.get(2), windowSize);
////
////
////                                                    lastIRInput.put(2, rawIR);
////                                                    lastIROutput.put(2, avgIR);
////
////
////
////                                                    addEntry(chart1, mymap.get(tempDevice.getMac()), (float) xValueTemp,(float) Float.valueOf(sensorData[2])); // IR
////                                                    addEntry(chart2, mymap.get(tempDevice.getMac()), (float) xValueTemp,(float)(avgIR)); // IR_ filtered
////                                                    addEntry(chart3, mymap.get(tempDevice.getMac()) , (float) xValueTemp,(float) Float.valueOf(sensorData[3])); // RED
////                                                    addEntry(chart4, mymap.get(tempDevice.getMac()) , (float) xValueTemp,(float) Float.valueOf(sensorData[4])); // RED
////                                                    addEntry(chart5, mymap.get(tempDevice.getMac()) , (float) xValueTemp,(float) Float.valueOf(sensorData[5])); // RED
////                                                    addEntry(chart6, mymap.get(tempDevice.getMac()) , (float) xValueTemp,(float) Float.valueOf(sensorData[6])); // RED
//
//                                                    // Add to dataset for eventual excel export
//
//
//                                                }
//
//
//
//
//
//                                            @Override
//                                            public void onReadFailure(final BleException exception) {
//                                                //showToast("Bluetooth unstable.  Move close to device");
//                                                //showToast(String.valueOf(exception));
//                                                //Error message comes here
//                                                Log.e("BLEconnect", "Failed because of: " + exception.toString() + " and "+ exception.getDescription());
//
//                                            }
 //                                       }
  //                              );


//                                // ✅ Nur EINMAL alles aktualisieren:
//                                chart1.getData().notifyDataChanged();
//                                chart1.notifyDataSetChanged();
//                                chart1.invalidate();
//
//                                chart2.getData().notifyDataChanged();
//                                chart2.notifyDataSetChanged();
//                                chart2.invalidate();



                            }
                            }
                        }
                    }
                }
            }
        }
    }



    private void initUI(View v) {
        characteristicList = new ArrayList<>();
        csvData = new ArrayList<>(10);
        mymap = new HashMap<String, Integer>();
        mymapName = new HashMap<Integer, String>();
        dataSetIndex = 0;

        addDevicesReminder = v.findViewById(R.id.addDevicesReminder);
        addDevicesReminder.bringToFront();

        txt_test = v.findViewById(R.id.testText);
        txt_test.setOnClickListener(this);

        btn_record = v.findViewById(R.id.btn_record);
        btn_record.setOnClickListener(this);

        btn_stop = v.findViewById(R.id.btn_save_record);
        btn_stop.setOnClickListener(this);
        btn_stop.setVisibility(v.GONE);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        xValueTemp = 0;
        graphStatus = false;
        displayGraphStatus = false;
        View v = inflater.inflate(R.layout.fragment_graph, container, false);
        initUI(v);

        if (savedInstanceState == null) {
            chart1 = initChart(chart1,v,R.id.chart1, 60f);
            chart2 = initChart(chart2,v,R.id.chart2, 60f);
            chart3 = initChart(chart3,v,R.id.chart3, 60f);

//            chart4 = initChart(chart4,v,R.id.chart4, 60f);
//
//            chart5 = initChart(chart5,v,R.id.chart5, 60f);
//
//            chart6 = initChart(chart6,v,R.id.chart6, 60f);


            rescaleAllCharts();
        }
        return v;
    }

    public void resetCharts(View v) {
        chart1 = initChart(chart1,v,R.id.chart1, 60f);
        chart2 = initChart(chart2,v,R.id.chart2, 60f);
        chart3 = initChart(chart3,v,R.id.chart3, 60f);
//        chart4 = initChart(chart4,v,R.id.chart4, 60f);
//
//        chart5 = initChart(chart5,v,R.id.chart5, 60f);
//
//        chart6 = initChart(chart6,v,R.id.chart6, 60f);
//


        rescaleAllCharts();

        xValueTemp = 0;
        graphStatus = false;
        displayGraphStatus = false;
        initUI(getView());
    }

    public void rescaleYAxis(LineChart lc) {
       /*
        LineData data = lc.getData();

        if (data == null) return;

        float max = Float.MIN_VALUE;
        float min = Float.MAX_VALUE;

        for (ILineDataSet set : data.getDataSets()) {
            for (int i = 0; i < set.getEntryCount(); i++) {
                float val = set.getEntryForIndex(i).getY();
                if (val > max) max = val;
                if (val < min) min = val;
            }
        }

        if (min == Float.MAX_VALUE || max == Float.MIN_VALUE) return;

        float range = max - min;
        YAxis leftAxis = lc.getAxisLeft();
        leftAxis.setAxisMinimum(min - 0.2f * range);
        leftAxis.setAxisMaximum(max + 0.2f * range);
        lc.notifyDataSetChanged();
        lc.invalidate();
,

        */

        float minY = Float.MAX_VALUE;
        float maxY = Float.MIN_VALUE;

        YAxis leftAxis = lc.getAxisLeft();
        // Get the current max and min values from the chart
        LineData data = lc.getData();
        List<ILineDataSet> b;
        ILineDataSet a;
        b = data.getDataSets();
        if (b.size()>1)
        {
         a = b.get(1);
        }
        else
        {   a = b.get(0);}

//        List<Entry> copiedEntries = new ArrayList<>(a.getEntryCount());
//        for (int i = 0; i < a.getEntryCount(); i++) {
//            Entry original = a.getEntryForIndex(i);
//            copiedEntries.add(new Entry(original.getX(), original.getY()));
//        }
//
//        int total = copiedEntries.size();
//        int startIndex = Math.max(0, total - 50);
//
//
//
//        for (int i = startIndex; i < total; i++) {
//            float y = copiedEntries.get(i).getY();
//            if (y < minY) minY = y;
//            if (y > maxY) maxY = y;
//        }




        minY = a.getYMin();
        maxY = a.getYMax();
//

/*
        float minY = Float.MAX_VALUE;
        float maxY = Float.MIN_VALUE;

        if (a.getEntryCount()>0)
        {
            int n = 200;
            int entryCount = a.getEntryCount();

            for (int i = Math.max(0, entryCount - n); i < entryCount; i++) {
                float y = a.getEntryForIndex(i).getY();
                if (!Float.isNaN(y) && !Float.isInfinite(y)) {
                    maxY = Math.max(maxY, y);
                    minY = Math.min(minY, y);
                }
            }
        }

 */


        float newMax = maxY + 0.05f * (maxY - minY);
        float newMin = minY - 0.01f * (maxY - minY);

        // Apply new scale
//        leftAxis.setAxisMaximum(maxY);
//        leftAxis.setAxisMinimum(minY);

        leftAxis.setAxisMaximum(newMax);
        leftAxis.setAxisMinimum(newMin);

        // Refresh the chart to apply changes
        lc.invalidate();
    }

    public void rescaleAllCharts() {
        rescaleYAxis(chart1);
        rescaleYAxis(chart2);
        rescaleYAxis(chart3);
//        rescaleYAxis(chart4);
//
//        rescaleYAxis(chart5);
//
//        rescaleYAxis(chart6);



    }

    private void addEntry(LineChart chartTemp, int dataSetIndex, float xVal, float yVal) {
        LineData data = chartTemp.getData();
        if (data != null) {
            ILineDataSet set = data.getDataSetByIndex(dataSetIndex);
            if (set == null) {
                set = createSet();
                data.addDataSet(set);
            }

            if (set.getEntryCount() >= 100) {
                // Remove the oldest entry (index 0)
                set.removeFirst();
            }



            //data.addEntry(new Entry(set.getEntryCount(), yVal),dataSetIndex);
            data.addEntry(new Entry(xVal, yVal),dataSetIndex);

//            if (chartUpdateCounter == 100) {
//                chartUpdateCounter=0;
//                chartTemp.notifyDataSetChanged();
//                chartTemp.invalidate();
//                data.notifyDataChanged();
//                chartTemp.setVisibleXRangeMaximum(5);
//
//            }

            chartTemp.notifyDataSetChanged();
            chartTemp.invalidate();
            data.notifyDataChanged();
            chartTemp.setVisibleXRangeMaximum(5);
//           chartTemp.notifyDataSetChanged();
            chartTemp.moveViewTo(xValueTemp-5, 01f, YAxis.AxisDependency.LEFT);
        }
    }

    private void addEntryToDataSet(LineChart chart, int dataSetIndex, float x, float y) {
        ILineDataSet dataSet = chart.getData().getDataSetByIndex(dataSetIndex);
        if (dataSet != null) {
            dataSet.addEntry(new Entry(x, y));
        }
    }

    private LineDataSet createSet() {
        LineDataSet set = new LineDataSet(null, "Dynamic Data");
        set.setAxisDependency(YAxis.AxisDependency.LEFT);
        set.setHighLightColor(Color.rgb(0,0,0));
        set.setColor(ColorTemplate.getHoloBlue());
        set.setCircleColor(Color.WHITE);
        set.setLineWidth(4f);
        set.setCircleRadius(4f);
        set.setFillAlpha(30);
        set.setFillColor(ColorTemplate.getHoloBlue());
        set.setHighLightColor(Color.rgb(244, 117, 117));
        set.setValueTextColor(Color.WHITE);
        set.setValueTextSize(9f);
        set.setDrawValues(false);
        return set;
    }

    private void Chart(LineChart chartTemp, View viewTemp, int viewID) {
        // Find Chart by ID
        chartTemp = (LineChart) viewTemp.findViewById(viewID);
        LineData data = chartTemp.getData();
    }

    private LineChart initChart(LineChart chartTemp, View viewTemp,int viewID, float rangeApprox) {
        // Find Chart by ID
        chartTemp = (LineChart) viewTemp.findViewById(viewID);

        // Formatting
        chartTemp.getDescription().setEnabled(true);
        chartTemp.getDescription().setText("");
        chartTemp.setDragEnabled(true);
        chartTemp.setScaleEnabled(true);
        chartTemp.setPinchZoom(true);
        chartTemp.getXAxis().setPosition(XAxis.XAxisPosition.BOTTOM);
        chartTemp.getAxisLeft().setTextColor(R.color.colorTextPrimary);
        chartTemp.getAxisRight().setTextColor(R.color.colorTextPrimary);
        chartTemp.getXAxis().setTextColor(R.color.colorTextPrimary);
        chartTemp.getXAxis().setTextSize(10);
        chartTemp.getAxisLeft().setTextSize(10);
        chartTemp.getAxisRight().setEnabled(false);
        chartTemp.getLegend().setTextColor(R.color.colorTextPrimary);
        chartTemp.getLegend().setForm(Legend.LegendForm.CIRCLE);
        chartTemp.setVisibleXRangeMaximum(3);



        final LineChart finalChartTemp = chartTemp;
        chartTemp.setOnChartValueSelectedListener(new OnChartValueSelectedListener()
        {
            @Override
            public void onValueSelected(Entry e, Highlight h)
            {
                float x=e.getX();
                float y=e.getY();
                finalChartTemp.getDescription().setText("x="+Float.toString(x) + ",y=" + Float.toString(y));
            }

            @Override
            public void onNothingSelected()
            {
                finalChartTemp.getDescription().setText("");
            }
        });

        Legend legend = chartTemp.getLegend();
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.TOP);
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.LEFT);
        legend.setOrientation(Legend.LegendOrientation.HORIZONTAL);
        legend.setDrawInside(false);

        // First Data Set

        int randColor = getRandomColor();
        ArrayList<Entry> yValues = new ArrayList<>();
        yValues.add(new Entry(0, 0));
        LineDataSet set1 = new LineDataSet(yValues,"");
        set1.setHighLightColor(Color.rgb(0,0,0));
        set1.setCircleRadius(1);
        set1.setFillColor(Color.rgb(250,250,250));
        set1.setCircleColor(Color.rgb(250,250,250));
        set1.setColor(Color.rgb(250,250,250));
        set1.setDrawValues(false);
        ArrayList<ILineDataSet> dataSets = new ArrayList<>();
        dataSets.add(set1);

        LineData data = new LineData(dataSets);
        chartTemp.setData(data);

        return chartTemp;
    }

    private LineDataSet initializeLineDataSet(String setName, int randomColor) {
        ArrayList<Entry> yValues = new ArrayList<>();
        LineDataSet setTemp = new LineDataSet(yValues,setName);
        setTemp.setHighLightColor(Color.rgb(0,0,0));
        setTemp.setCircleRadius(1);

        setTemp.setFillColor(Color.RED);

        setTemp.setCircleColor(randomColor);
        setTemp.setColor(randomColor);

        setTemp.setLineWidth(2f);

        setTemp.setFillAlpha(50);

        setTemp.setDrawFilled(true);
        setTemp.setDrawValues(false);

        setTemp.setMode(LineDataSet.Mode.LINEAR);

        return setTemp;
    }

    final Random mRandom = new Random(System.currentTimeMillis());

    public int getRandomColor() {
        Random rand = new Random();
        int r = rand.nextInt(6); //increase number to add more colors

        int[] randomColor = new int[10];
        randomColor[0] = Color.rgb(244, 67, 54);
        randomColor[1] = Color.rgb(236, 64, 122);
//        randomColor[2] = Color.rgb(31, 35, 194);
//        randomColor[3] = Color.rgb(30, 196, 181);
//        randomColor[4] = Color.rgb(85, 230, 98);
//        randomColor[5] = Color.rgb(85, 230, 98);
        //TODO: add rgb values for random colors ;)

        r = r - 1;
        if (r <= 0) {r = 0;}
        if(r > 5) r = 5;
        return randomColor[r];
    }


    private void showToast (String msg) {
        Toast.makeText(getActivity(), msg, Toast.LENGTH_SHORT).show();
    }

    private void showToast (String msg, Context context) {
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show();
    }

    // BLE DECODER
    private static String hexToAscii(String hexStr) {
        StringBuilder output = new StringBuilder("");

        for (int i = 0; i < hexStr.length(); i += 2) {
            String str = hexStr.substring(i, i + 2);
            output.append((char) Integer.parseInt(str, 16));
        }

        return output.toString();
    }

    private static String getStringRepresentation(ArrayList<Character> list)
    {
        StringBuilder builder = new StringBuilder(list.size());
        for(Character ch: list)
        {
            builder.append(ch);
        }
        return builder.toString();
    }

//    private static String[] unpackageBLEPacket(String packetData)
//    {
//        String[] sensorData;
//        try {
//            sensorData = packetData.split("/");
//            for (int i = 0; i < sensorData.length; i++) {
//                sensorData[i] = sensorData[i].substring(1);
//            }
//        }
//        catch (Exception ee) {
//            String[] errorHolder = new String[2];
//            errorHolder[0] = "Array";
//            errorHolder[1] = "Failed";
//            sensorData = errorHolder;
//        }
//        return sensorData;
//    }

    private static String[][] unpackageBLEPacketMulti(String packetData) {
        String[][] parsedSamples;

        try {
            // 1. Aufteilen in einzelne Pakete (beginnt immer mit 'B...')
            String[] packets = packetData.split("(?=B)");

            parsedSamples = new String[packets.length][];
            for (int i = 0; i < packets.length; i++) {
                String[] fields = packets[i].split("/");

                // Nur wenn mindestens 6 Werte vorhanden sind (B/C/D/E/F/G)
                if (fields.length >= 6) {
                    String[] oneSample = new String[6];  // B, C, D, E, F, G

                    for (int j = 0; j < 6; j++) {
                        // Nur wenn das Feld lang genug ist
                        if (fields[j].length() > 1) {
                            oneSample[j] = fields[j].substring(1);  // entferne Buchstabenpräfix
                        } else {
                            oneSample[j] = "0";  // Fallback
                        }
                    }
                    parsedSamples[i] = oneSample;
                } else {
                    // Ungültiges Paket – fülle mit Nullen
                    parsedSamples[i] = new String[]{"0", "0", "0", "0", "0", "0"};
                }
            }

        } catch (Exception e) {
            // Fehlerbehandlung – gib nur ein Sample mit Fehlermeldung zurück
            parsedSamples = new String[1][2];
            parsedSamples[0][0] = "Array";
            parsedSamples[0][1] = "Failed";
        }

        return parsedSamples;
    }




    private Thread thread;

    public void startGraphing() {
        if (thread != null) {thread.interrupt();}
        final Runnable runnable = new Runnable() {
            int counter = 0;

            @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
            @Override
            public void run() {
                if (graphStatus) {
                    getData();
                    //Call for sudden disconnection
                    if(!((MainActivity)getActivity()).getIsConnected()){
                        //showToast("Sudden Disconnection, data is saved");
                        btn_record.setBackgroundResource(R.drawable.ic_baseline_fiber_manual_record_24);
                        btn_stop.setVisibility(getView().GONE);
                        suddenDisconnectAlert();
                        graphStatus = false;
                        addDevicesReminder.setVisibility(View.INVISIBLE);
                        addDevicesReminder.setVisibility(View.VISIBLE);
                    }
                }
                if (counter < 10 || counter%100 == 0) {
                    rescaleAllCharts();
                }
                counter=counter + 1;
            }
        };
        thread = new Thread(new Runnable() {
            @Override
            public void run() {
                while (graphStatus) {
                    getActivity().runOnUiThread(runnable);
                    try {
                        // every one second a new sample of data
                        Thread.sleep(50);
                     //   xValueTemp = xValueTemp + (float) 0.01;
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        Log.e(TAG, e.getMessage());
                    }
                }
            }
        });
        thread.start();
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_record:
                if (graphStatus) {
                    // DO THIS WHEN RECORDING
                    btn_record.setBackgroundResource(R.drawable.ic_baseline_fiber_manual_record_24);
                    graphStatus = false;
                }
                else {
                    // EXIT RECORD
                    if( ((MainActivity)getActivity()).getIsConnected()) {
                        if(((MainActivity) getActivity()).getIsGraphing()) {
                            graphStatus = true;
                            startGraphing();
                            btn_stop.setVisibility(getView().VISIBLE);
                            btn_record.setBackgroundResource(R.drawable.ic_baseline_pause_24);
                        }
                        else{
                            showToast("Graphing is not activated");
                        }
                    }
                    else {
                        showToast("No device connected");
                    }
                }
                break;
            case R.id.btn_save_record:
                btn_record.setBackgroundResource(R.drawable.ic_baseline_fiber_manual_record_24);
                btn_stop.setVisibility(getView().GONE);
                //exportData(getView());
                saveDataToCSV();
                graphStatus = false;
                addDevicesReminder.setVisibility(View.INVISIBLE);
                break;
        }
    }

    private void suddenDisconnectAlert(){
        new AlertDialog.Builder(getContext())
                .setTitle("Sudden Disconnection to BLE device")
                .setMessage("Recorded data can be saved")
                .setPositiveButton("Confirm", (dialog, which) -> {
                    saveDataToCSV();
                })
                .setCancelable(false) // Prevent back button or outside touch to cancel
                .show();
    }

    public void saveDataToCSV() {
        AlertDialog.Builder alert = new AlertDialog.Builder(getActivity());
        alert.setTitle("File Name");
        alert.setMessage("Save file as:");
        // Set an EditText view to get user input
        final EditText input = new EditText(getActivity());

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd(H_mm_ss)", Locale.US);
        Date now = new Date();
        final String fileName = formatter.format(now);

        input.setText(fileName);
        alert.setView(input);

        alert.setPositiveButton("Ok", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int whichButton) {
                Editable value = input.getText();

                //showToast(value.toString());
                // ADD CODE AFTER CONFIRMATION OF FILE SAVE
                exportCSV(getView(), fileName,data_txt);
                resetCharts(getView());
            }
        });
        alert.setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int whichButton) {
                AlertDialog.Builder cancelAlert = new AlertDialog.Builder(getActivity());
                cancelAlert.setTitle("Confirm data deletion");
                cancelAlert.setMessage("Are you sure you don't want to save?");
                cancelAlert.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int whichButton) {
                        resetCharts(getView());
                    }
                });
                cancelAlert.setNegativeButton("No", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int whichButton) {
                        saveDataToCSV();
                    }
                });
                cancelAlert.show();
            }
        });
        alert.show();
    }

//    private void filteredData(LineChart chartTemp, int windowSize) {
//        LineData data = chartTemp.getData();
//        List<Entry> smoothedEntries = new ArrayList<>();
//
//        ILineDataSet set = data.getDataSetByIndex(dataSetIndex);
//        if (set != null & set.getEntryCount() < windowSize){
//        for (int i = 0; i < set.getEntryCount(); i++) {
//            float sum = 0;
//            int count = 0;
//
//            // Compute the moving average window
//            for (int j = i - windowSize / 2; j <= i + windowSize / 2; j++) {
//                if (j >= 0 && j < set.getEntryCount()) {
//                    sum += set.getEntryForIndex(j).getY();
//                    count++;
//                }
//            }
//
//            float avg = sum / count;
//            Entry original = set.getEntryForIndex(i);
//            smoothedEntries.add(new Entry(original.getX(), avg));
//        }
//            set.clear();
//            for (Entry e : smoothedEntries) {
//                set.addEntry(e);
//            }
//
//            data.notifyDataChanged();
//            chartTemp.notifyDataSetChanged();
//            chartTemp.invalidate();
//
//    }
//    }


    private float applyHighPassFilter(float currentInput, float lastInput, float lastOutput, float alpha) {

        return alpha * (lastOutput + currentInput - lastInput);

    }



    private float calculateMovingAverage(List<Float> values, int windowSize) {

        int size = values.size();

        if (size < windowSize) return values.get(size - 1);  // not enough values yet



        float sum = 0;

        for (int i = size - windowSize; i < size; i++) {

            sum += values.get(i);

        }

        return sum / windowSize;

    }
}


