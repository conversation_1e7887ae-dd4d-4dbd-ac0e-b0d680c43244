package com.clj.blesamplePCBA.data;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.text.Editable;
import android.view.View;
import android.widget.EditText;
import android.widget.Toast;

import androidx.core.content.FileProvider;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

//Andrej: potential implementation to save CSV data globally when interrupt
//Singleton (as enum for Thread security)
public enum CSVDataSaver {
    INSTANCE;

    private CSVDataSaver() {
    }

    public CSVDataSaver getInstance() {
        return INSTANCE;
    }
    /*
    public void saveDataToCSV(Activity thisActivity) {
        AlertDialog.Builder alert = new AlertDialog.Builder(thisActivity);
        alert.setTitle("File Name");
        alert.setMessage("Save file as:");
        // Set an EditText view to get user input
        final EditText input = new EditText(thisActivity);

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd(H_mm_ss)", Locale.US);
        Date now = new Date();
        final String fileName = formatter.format(now);

        input.setText(fileName);
        alert.setView(input);

        alert.setPositiveButton("Ok", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int whichButton) {
                Editable value = input.getText();
                //showToast(value.toString());
                // ADD CODE AFTER CONFIRMATION OF FILE SAVE
                exportCSV(getView(), fileName);
                resetCharts(getView());
            }
        });
        alert.setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int whichButton) {
                AlertDialog.Builder cancelAlert = new AlertDialog.Builder(This_activity);
                cancelAlert.setTitle("Confirm data deletion");
                cancelAlert.setMessage("Are you sure you don't want to save?");
                cancelAlert.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int whichButton) {
                        resetCharts(getView());
                    }
                });
                cancelAlert.setNegativeButton("No", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int whichButton) {
                        saveDataToCSV();
                    }
                });
                cancelAlert.show();
            }
        });
        alert.show();
    }

    public void exportCSV(Activity thisActivity, View v, String csvDefaultName) {
        for (int sheetID = 0; sheetID < csvData.size();sheetID++) {
            //showToast(mymapName.get(0));
            String csvName = mymapName.get(sheetID+1) + "_" + csvDefaultName;
            StringBuilder data = new StringBuilder();
            for (int rowID = 0; rowID < csvData.get(sheetID).size();rowID++) {
                for (int columnID = 0; columnID < csvData.get(sheetID).get(rowID).size();columnID++) {
                    if (columnID == 0) {
                        data.append(csvData.get(sheetID).get(rowID).get(columnID));
                    }
                    else {
                        data.append(","+csvData.get(sheetID).get(rowID).get(columnID));
                    }
                }
                data.append("\n");
            }
            try{
                //saving the file into device
                Context context = thisActivity.getApplicationContext();
                FileOutputStream out = context.openFileOutput(csvName+".csv", Context.MODE_PRIVATE);
                out.write((data.toString()).getBytes());
                out.close();

                //exporting
                //context = getActivity().getApplicationContext();
                File filelocation = new File(context.getFilesDir(), csvName+".csv");
                Uri path = FileProvider.getUriForFile(context, "com.example.exportcsv.fileprovider", filelocation);
                showToast("File saved to " + path);
                Intent fileIntent = new Intent(Intent.ACTION_SEND);
                fileIntent.setType("text/csv");
                fileIntent.putExtra(Intent.EXTRA_SUBJECT, csvName); // File Name
                fileIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                fileIntent.putExtra(Intent.EXTRA_STREAM, path);
                startActivity(Intent.createChooser(fileIntent, "Send mail"));
            }
            catch(Exception e){
                e.printStackTrace();
            }
        }
    }
    */
}