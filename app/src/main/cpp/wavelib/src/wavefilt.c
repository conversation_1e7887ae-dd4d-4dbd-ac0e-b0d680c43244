/*
* Copyright (c) 2014, <PERSON><PERSON><PERSON>
* Copyright (C) 2016  <PERSON><PERSON><PERSON>
* Daubechies wavelets coefficents Computed by <PERSON><PERSON><PERSON>, Aichi Institute of Technology. http://phase.hpcc.jp/phase/wavelet/
*/
#include "wavefilt.h"


static const double db1[2] = {
    7.071067811865475244008443621048490392848359376884740365883398e-01,
    7.071067811865475244008443621048490392848359376884740365883398e-01
};

static const double db2[4] = {
    4.829629131445341433748715998644486838169524195042022752011715e-01,
    8.365163037378079055752937809168732034593703883484392934953414e-01,
    2.241438680420133810259727622404003554678835181842717613871683e-01,
    -1.294095225512603811744494188120241641745344506599652569070016e-01
};


static const double db3[6] = {
    3.326705529500826159985115891390056300129233992450683597084705e-01,
    8.068915093110925764944936040887134905192973949948236181650920e-01,
    4.598775021184915700951519421476167208081101774314923066433867e-01,
    -1.350110200102545886963899066993744805622198452237811919756862e-01,
    -8.544127388202666169281916918177331153619763898808662976351748e-02,
    3.522629188570953660274066471551002932775838791743161039893406e-02
};


static const double db4[8] = {
    2.303778133088965008632911830440708500016152482483092977910968e-01,
    7.148465705529156470899219552739926037076084010993081758450110e-01,
    6.308807679298589078817163383006152202032229226771951174057473e-01,
    -2.798376941685985421141374718007538541198732022449175284003358e-02,
    -1.870348117190930840795706727890814195845441743745800912057770e-01,
    3.084138183556076362721936253495905017031482172003403341821219e-02,
    3.288301166688519973540751354924438866454194113754971259727278e-02,
    -1.059740178506903210488320852402722918109996490637641983484974e-02
};


static const double db5[10] = {
    1.601023979741929144807237480204207336505441246250578327725699e-01,
    6.038292697971896705401193065250621075074221631016986987969283e-01,
    7.243085284377729277280712441022186407687562182320073725767335e-01,
    1.384281459013207315053971463390246973141057911739561022694652e-01,
    -2.422948870663820318625713794746163619914908080626185983913726e-01,
    -3.224486958463837464847975506213492831356498416379847225434268e-02,
    7.757149384004571352313048938860181980623099452012527983210146e-02,
    -6.241490212798274274190519112920192970763557165687607323417435e-03,
    -1.258075199908199946850973993177579294920459162609785020169232e-02,
    3.335725285473771277998183415817355747636524742305315099706428e-03
};


static const double db6[12] = {
    1.115407433501094636213239172409234390425395919844216759082360e-01,
    4.946238903984530856772041768778555886377863828962743623531834e-01,
    7.511339080210953506789344984397316855802547833382612009730420e-01,
    3.152503517091976290859896548109263966495199235172945244404163e-01,
    -2.262646939654398200763145006609034656705401539728969940143487e-01,
    -1.297668675672619355622896058765854608452337492235814701599310e-01,
    9.750160558732304910234355253812534233983074749525514279893193e-02,
    2.752286553030572862554083950419321365738758783043454321494202e-02,
    -3.158203931748602956507908069984866905747953237314842337511464e-02,
    5.538422011614961392519183980465012206110262773864964295476524e-04,
    4.777257510945510639635975246820707050230501216581434297593254e-03,
    -1.077301085308479564852621609587200035235233609334419689818580e-03
};


static const double db7[14] = {
    7.785205408500917901996352195789374837918305292795568438702937e-02,
    3.965393194819173065390003909368428563587151149333287401110499e-01,
    7.291320908462351199169430703392820517179660611901363782697715e-01,
    4.697822874051931224715911609744517386817913056787359532392529e-01,
    -1.439060039285649754050683622130460017952735705499084834401753e-01,
    -2.240361849938749826381404202332509644757830896773246552665095e-01,
    7.130921926683026475087657050112904822711327451412314659575113e-02,
    8.061260915108307191292248035938190585823820965629489058139218e-02,
    -3.802993693501441357959206160185803585446196938467869898283122e-02,
    -1.657454163066688065410767489170265479204504394820713705239272e-02,
    1.255099855609984061298988603418777957289474046048710038411818e-02,
    4.295779729213665211321291228197322228235350396942409742946366e-04,
    -1.801640704047490915268262912739550962585651469641090625323864e-03,
    3.537137999745202484462958363064254310959060059520040012524275e-04
};

static const double db8[16] = {
    5.441584224310400995500940520299935503599554294733050397729280e-02,
    3.128715909142999706591623755057177219497319740370229185698712e-01,
    6.756307362972898068078007670471831499869115906336364227766759e-01,
    5.853546836542067127712655200450981944303266678053369055707175e-01,
    -1.582910525634930566738054787646630415774471154502826559735335e-02,
    -2.840155429615469265162031323741647324684350124871451793599204e-01,
    4.724845739132827703605900098258949861948011288770074644084096e-04,
    1.287474266204784588570292875097083843022601575556488795577000e-01,
    -1.736930100180754616961614886809598311413086529488394316977315e-02,
    -4.408825393079475150676372323896350189751839190110996472750391e-02,
    1.398102791739828164872293057263345144239559532934347169146368e-02,
    8.746094047405776716382743246475640180402147081140676742686747e-03,
    -4.870352993451574310422181557109824016634978512157003764736208e-03,
    -3.917403733769470462980803573237762675229350073890493724492694e-04,
    6.754494064505693663695475738792991218489630013558432103617077e-04,
    -1.174767841247695337306282316988909444086693950311503927620013e-04
};


static const double db9[18] = {
    3.807794736387834658869765887955118448771714496278417476647192e-02,
    2.438346746125903537320415816492844155263611085609231361429088e-01,
    6.048231236901111119030768674342361708959562711896117565333713e-01,
    6.572880780513005380782126390451732140305858669245918854436034e-01,
    1.331973858250075761909549458997955536921780768433661136154346e-01,
    -2.932737832791749088064031952421987310438961628589906825725112e-01,
    -9.684078322297646051350813353769660224825458104599099679471267e-02,
    1.485407493381063801350727175060423024791258577280603060771649e-01,
    3.072568147933337921231740072037882714105805024670744781503060e-02,
    -6.763282906132997367564227482971901592578790871353739900748331e-02,
    2.509471148314519575871897499885543315176271993709633321834164e-04,
    2.236166212367909720537378270269095241855646688308853754721816e-02,
    -4.723204757751397277925707848242465405729514912627938018758526e-03,
    -4.281503682463429834496795002314531876481181811463288374860455e-03,
    1.847646883056226476619129491125677051121081359600318160732515e-03,
    2.303857635231959672052163928245421692940662052463711972260006e-04,
    -2.519631889427101369749886842878606607282181543478028214134265e-04,
    3.934732031627159948068988306589150707782477055517013507359938e-05
};


static const double db10[20] = {
    2.667005790055555358661744877130858277192498290851289932779975e-02,
    1.881768000776914890208929736790939942702546758640393484348595e-01,
    5.272011889317255864817448279595081924981402680840223445318549e-01,
    6.884590394536035657418717825492358539771364042407339537279681e-01,
    2.811723436605774607487269984455892876243888859026150413831543e-01,
    -2.498464243273153794161018979207791000564669737132073715013121e-01,
    -1.959462743773770435042992543190981318766776476382778474396781e-01,
    1.273693403357932600826772332014009770786177480422245995563097e-01,
    9.305736460357235116035228983545273226942917998946925868063974e-02,
    -7.139414716639708714533609307605064767292611983702150917523756e-02,
    -2.945753682187581285828323760141839199388200516064948779769654e-02,
    3.321267405934100173976365318215912897978337413267096043323351e-02,
    3.606553566956169655423291417133403299517350518618994762730612e-03,
    -1.073317548333057504431811410651364448111548781143923213370333e-02,
    1.395351747052901165789318447957707567660542855688552426721117e-03,
    1.992405295185056117158742242640643211762555365514105280067936e-03,
    -6.858566949597116265613709819265714196625043336786920516211903e-04,
    -1.164668551292854509514809710258991891527461854347597362819235e-04,
    9.358867032006959133405013034222854399688456215297276443521873e-05,
    -1.326420289452124481243667531226683305749240960605829756400674e-05
};

static const double db11[22] = {
    1.869429776147108402543572939561975728967774455921958543286692e-02,
    1.440670211506245127951915849361001143023718967556239604318852e-01,
    4.498997643560453347688940373853603677806895378648933474599655e-01,
    6.856867749162005111209386316963097935940204964567703495051589e-01,
    4.119643689479074629259396485710667307430400410187845315697242e-01,
    -1.622752450274903622405827269985511540744264324212130209649667e-01,
    -2.742308468179469612021009452835266628648089521775178221905778e-01,
    6.604358819668319190061457888126302656753142168940791541113457e-02,
    1.498120124663784964066562617044193298588272420267484653796909e-01,
    -4.647995511668418727161722589023744577223260966848260747450320e-02,
    -6.643878569502520527899215536971203191819566896079739622858574e-02,
    3.133509021904607603094798408303144536358105680880031964936445e-02,
    2.084090436018106302294811255656491015157761832734715691126692e-02,
    -1.536482090620159942619811609958822744014326495773000120205848e-02,
    -3.340858873014445606090808617982406101930658359499190845656731e-03,
    4.928417656059041123170739741708273690285547729915802418397458e-03,
    -3.085928588151431651754590726278953307180216605078488581921562e-04,
    -8.930232506662646133900824622648653989879519878620728793133358e-04,
    2.491525235528234988712216872666801088221199302855425381971392e-04,
    5.443907469936847167357856879576832191936678525600793978043688e-05,
    -3.463498418698499554128085159974043214506488048233458035943601e-05,
    4.494274277236510095415648282310130916410497987383753460571741e-06
};

static const double db12[24] = {
    1.311225795722951750674609088893328065665510641931325007748280e-02,
    1.095662728211851546057045050248905426075680503066774046383657e-01,
    3.773551352142126570928212604879206149010941706057526334705839e-01,
    6.571987225793070893027611286641169834250203289988412141394281e-01,
    5.158864784278156087560326480543032700677693087036090056127647e-01,
    -4.476388565377462666762747311540166529284543631505924139071704e-02,
    -3.161784537527855368648029353478031098508839032547364389574203e-01,
    -2.377925725606972768399754609133225784553366558331741152482612e-02,
    1.824786059275796798540436116189241710294771448096302698329011e-01,
    5.359569674352150328276276729768332288862665184192705821636342e-03,
    -9.643212009650708202650320534322484127430880143045220514346402e-02,
    1.084913025582218438089010237748152188661630567603334659322512e-02,
    4.154627749508444073927094681906574864513532221388374861287078e-02,
    -1.221864906974828071998798266471567712982466093116558175344811e-02,
    -1.284082519830068329466034471894728496206109832314097633275225e-02,
    6.711499008795509177767027068215672450648112185856456740379455e-03,
    2.248607240995237599950865211267234018343199786146177099262010e-03,
    -2.179503618627760471598903379584171187840075291860571264980942e-03,
    6.545128212509595566500430399327110729111770568897356630714552e-06,
    3.886530628209314435897288837795981791917488573420177523436096e-04,
    -8.850410920820432420821645961553726598738322151471932808015443e-05,
    -2.424154575703078402978915320531719580423778362664282239377532e-05,
    1.277695221937976658714046362616620887375960941439428756055353e-05,
    -1.529071758068510902712239164522901223197615439660340672602696e-06
};

static const double db13[26] = {
    9.202133538962367972970163475644184667534171916416562386009703e-03,
    8.286124387290277964432027131230466405208113332890135072514277e-02,
    3.119963221604380633960784112214049693946683528967180317160390e-01,
    6.110558511587876528211995136744180562073612676018239438526582e-01,
    5.888895704312189080710395347395333927665986382812836042235573e-01,
    8.698572617964723731023739838087494399231884076619701250882016e-02,
    -3.149729077113886329981698255932282582876888450678789025950306e-01,
    -1.245767307508152589413808336021260180792739295173634719572069e-01,
    1.794760794293398432348450072339369013581966256244133393042881e-01,
    7.294893365677716380902830610477661983325929026879873553627963e-02,
    -1.058076181879343264509667304196464849478860754801236658232360e-01,
    -2.648840647534369463963912248034785726419604844297697016264224e-02,
    5.613947710028342886214501998387331119988378792543100244737056e-02,
    2.379972254059078811465170958554208358094394612051934868475139e-03,
    -2.383142071032364903206403067757739134252922717636226274077298e-02,
    3.923941448797416243316370220815526558824746623451404043918407e-03,
    7.255589401617566194518393300502698898973529679646683695269828e-03,
    -2.761911234656862178014576266098445995350093330501818024966316e-03,
    -1.315673911892298936613835370593643376060412592653652307238124e-03,
    9.323261308672633862226517802548514100918088299801952307991569e-04,
    4.925152512628946192140957387866596210103778299388823500840094e-05,
    -1.651289885565054894616687709238000755898548214659776703347801e-04,
    3.067853757932549346649483228575476236600428217237900563128230e-05,
    1.044193057140813708170714991080596951670706436217328169641474e-05,
    -4.700416479360868325650195165061771321650383582970958556568059e-06,
    5.220035098454864691736424354843176976747052155243557001531901e-07
};

static const double db14[28] = {
    6.461153460087947818166397448622814272327159419201199218101404e-03,
    6.236475884939889832798566758434877428305333693407667164602518e-02,
    2.548502677926213536659077886778286686187042416367137443780084e-01,
    5.543056179408938359926831449851154844078269830951634609683997e-01,
    6.311878491048567795576617135358172348623952456570017289788809e-01,
    2.186706877589065214917475918217517051765774321270432059030273e-01,
    -2.716885522787480414142192476181171094604882465683330814311896e-01,
    -2.180335299932760447555558812702311911975240669470604752747127e-01,
    1.383952138648065910739939690021573713989900463229686119059119e-01,
    1.399890165844607012492943162271163440328221555614326181333683e-01,
    -8.674841156816968904560822066727795382979149539517503657492964e-02,
    -7.154895550404613073584145115173807990958069673129538099990913e-02,
    5.523712625921604411618834060533403397913833632511672157671107e-02,
    2.698140830791291697399031403215193343375766595807274233284349e-02,
    -3.018535154039063518714822623489137573781575406658652624883756e-02,
    -5.615049530356959133218371367691498637457297203925810387698680e-03,
    1.278949326633340896157330705784079299374903861572058313481534e-02,
    -7.462189892683849371817160739181780971958187988813302900435487e-04,
    -3.849638868022187445786349316095551774096818508285700493058915e-03,
    1.061691085606761843032566749388411173033941582147830863893939e-03,
    7.080211542355278586442977697617128983471863464181595371670094e-04,
    -3.868319473129544821076663398057314427328902107842165379901468e-04,
    -4.177724577037259735267979539839258928389726590132730131054323e-05,
    6.875504252697509603873437021628031601890370687651875279882727e-05,
    -1.033720918457077394661407342594814586269272509490744850691443e-05,
    -4.389704901781394115254042561367169829323085360800825718151049e-06,
    1.724994675367812769885712692741798523587894709867356576910717e-06,
    -1.787139968311359076334192938470839343882990309976959446994022e-07
};

static const double db15[30] = {
    4.538537361578898881459394910211696346663671243788786997916513e-03,
    4.674339489276627189170969334843575776579151700214943513113197e-02,
    2.060238639869957315398915009476307219306138505641930902702047e-01,
    4.926317717081396236067757074029946372617221565130932402160160e-01,
    6.458131403574243581764209120106917996432608287494046181071489e-01,
    3.390025354547315276912641143835773918756769491793554669336690e-01,
    -1.932041396091454287063990534321471746304090039142863827937754e-01,
    -2.888825965669656462484125009822332981311435630435342594971292e-01,
    6.528295284877281692283107919869574882039174285596144125965101e-02,
    1.901467140071229823484893116586020517959501258174336696878156e-01,
    -3.966617655579094448384366751896200668381742820683736805449745e-02,
    -1.111209360372316933656710324674058608858623762165914120505657e-01,
    3.387714392350768620854817844433523770864744687411265369463195e-02,
    5.478055058450761268913790312581879108609415997422768564244845e-02,
    -2.576700732843996258594525754269826392203641634825340138396836e-02,
    -2.081005016969308167788483424677000162054657951364899040996166e-02,
    1.508391802783590236329274460170322736244892823305627716233968e-02,
    5.101000360407543169708860185565314724801066527344222055526631e-03,
    -6.487734560315744995181683149218690816955845639388826407928967e-03,
    -2.417564907616242811667225326300179605229946995814535223329411e-04,
    1.943323980382211541764912332541087441011424865579531401452302e-03,
    -3.734823541376169920098094213645414611387630968030256625740226e-04,
    -3.595652443624688121649620075909808858194202454084090305627480e-04,
    1.558964899205997479471658241227108816255567059625495915228603e-04,
    2.579269915531893680925862417616855912944042368767340709160119e-05,
    -2.813329626604781364755324777078478665791443876293788904267255e-05,
    3.362987181737579803124845210420177472134846655864078187186304e-06,
    1.811270407940577083768510912285841160577085925337507850590290e-06,
    -6.316882325881664421201597299517657654166137915121195510416641e-07,
    6.133359913305752029056299460289788601989190450885396512173845e-08
};

static const double db16[32] = {
    3.189220925347738029769547564645958687067086750131428767875878e-03,
    3.490771432367334641030147224023020009218241430503984146140054e-02,
    1.650642834888531178991252730561134811584835002342723240213592e-01,
    4.303127228460038137403925424357684620633970478036986773924646e-01,
    6.373563320837888986319852412996030536498595940814198125967751e-01,
    4.402902568863569000390869163571679288527803035135272578789884e-01,
    -8.975108940248964285718718077442597430659247445582660149624718e-02,
    -3.270633105279177046462905675689119641757228918228812428141723e-01,
    -2.791820813302827668264519595026873204339971219174736041535479e-02,
    2.111906939471042887209680163268837900928491426167679439251042e-01,
    2.734026375271604136485245757201617965429027819507130220231500e-02,
    -1.323883055638103904500474147756493375092287817706027978798549e-01,
    -6.239722752474871765674503394120025865444656311678760990761458e-03,
    7.592423604427631582148498743941422461530405946100943351940313e-02,
    -7.588974368857737638494890864636995796586975144990925400097160e-03,
    -3.688839769173014233352666320894554314718748429706730831064068e-02,
    1.029765964095596941165000580076616900528856265803662208854147e-02,
    1.399376885982873102950451873670329726409840291727868988490100e-02,
    -6.990014563413916670284249536517288338057856199646469078115759e-03,
    -3.644279621498389932169000540933629387055333973353108668841215e-03,
    3.128023381206268831661202559854678767821471906193608117450360e-03,
    4.078969808497128362417470323406095782431952972310546715071397e-04,
    -9.410217493595675889266453953635875407754747216734480509250273e-04,
    1.142415200387223926440228099555662945839684344936472652877091e-04,
    1.747872452253381803801758637660746874986024728615399897971953e-04,
    -6.103596621410935835162369150522212811957259981965919143961722e-05,
    -1.394566898820889345199078311998401982325273569198675335408707e-05,
    1.133660866127625858758848762886536997519471068203753661757843e-05,
    -1.043571342311606501525454737262615404887478930635676471546032e-06,
    -7.363656785451205512099695719725563646585445545841663327433569e-07,
    2.308784086857545866405412732942006121306306735866655525372544e-07,
    -2.109339630100743097000572623603489906836297584591605307745349e-08
};

static const double db17[34] = {
    2.241807001037312853535962677074436914062191880560370733250531e-03,
    2.598539370360604338914864591720788315473944524878241294399948e-02,
    1.312149033078244065775506231859069960144293609259978530067004e-01,
    3.703507241526411504492548190721886449477078876896803823650425e-01,
    6.109966156846228181886678867679372082737093893358726291371783e-01,
    5.183157640569378393254538528085968046216817197718416402439904e-01,
    2.731497040329363500431250719147586480350469818964563003672942e-02,
    -3.283207483639617360909665340725061767581597698151558024679130e-01,
    -1.265997522158827028744679110933825505053966260104086162103728e-01,
    1.973105895650109927854047044781930142551422414135646917122284e-01,
    1.011354891774702721509699856433434802196622545499664876109437e-01,
    -1.268156917782863110948571128662331680384792185915017065732137e-01,
    -5.709141963167692728911239478651382324161160869845347053990144e-02,
    8.110598665416088507965885748555429201024364190954499194020678e-02,
    2.231233617810379595339136059534813756232242114093689244020869e-02,
    -4.692243838926973733300897059211400507138768125498030602878439e-02,
    -3.270955535819293781655360222177494452069525958061609392809275e-03,
    2.273367658394627031845616244788448969906713741338339498024864e-02,
    -3.042989981354637068592482637907206078633395457225096588287881e-03,
    -8.602921520322854831713706413243659917926736284271730611920986e-03,
    2.967996691526094872806485060008038269959463846548378995044195e-03,
    2.301205242153545624302059869038423604241976680189447476064764e-03,
    -1.436845304802976126222890402980384903503674530729935809561434e-03,
    -3.281325194098379713954444017520115075812402442728749700195651e-04,
    4.394654277686436778385677527317841632289249319738892179465910e-04,
    -2.561010956654845882729891210949920221664082061531909655178413e-05,
    -8.204803202453391839095482576282189866136273049636764338689593e-05,
    2.318681379874595084482068205706277572106695174091895338530734e-05,
    6.990600985076751273204549700855378627762758585902057964027481e-06,
    -4.505942477222988194102268206378312129713572600716499944918416e-06,
    3.016549609994557415605207594879939763476168705217646897702706e-07,
    2.957700933316856754979905258816151367870345628924317307354639e-07,
    -8.423948446002680178787071296922877068410310942222799622593133e-08,
    7.267492968561608110879767441409035034158581719789791088892046e-09
};

static const double db18[36] = {
    1.576310218440760431540744929939777747670753710991660363684429e-03,
    1.928853172414637705921391715829052419954667025288497572236714e-02,
    1.035884658224235962241910491937253596470696555220241672976224e-01,
    3.146789413370316990571998255652579931786706190489374509491307e-01,
    5.718268077666072234818589370900623419393673743130930561295324e-01,
    5.718016548886513352891119994065965025668047882818525060759395e-01,
    1.472231119699281415750977271081072312557864107355701387801677e-01,
    -2.936540407365587442479030994981150723935710729035053239661752e-01,
    -2.164809340051429711237678625668271471437937235669492408388692e-01,
    1.495339755653777893509301738913667208804816691893765610261943e-01,
    1.670813127632574045149318139950134745324205646353988083152250e-01,
    -9.233188415084628060429372558659459731431848000144569612074508e-02,
    -1.067522466598284855932200581614984861385266404624112083917702e-01,
    6.488721621190544281947577955141911463129382116634147846137149e-02,
    5.705124773853688412090768846499622260596226120431038524600676e-02,
    -4.452614190298232471556143559744653492971477891439833592755034e-02,
    -2.373321039586000103275209582665216110197519330713490233071565e-02,
    2.667070592647059029987908631672020343207895999936072813363471e-02,
    6.262167954305707485236093144497882501990325204745013190268052e-03,
    -1.305148094661200177277636447600807169755191054507571666606133e-02,
    1.186300338581174657301741592161819084544899417452317405185615e-04,
    4.943343605466738130665529516802974834299638313366477765295203e-03,
    -1.118732666992497072800658855238650182318060482584970145512687e-03,
    -1.340596298336106629517567228251583609823044524685986640323942e-03,
    6.284656829651457125619449885420838217551022796301582874349652e-04,
    2.135815619103406884039052814341926025873200325996466522543440e-04,
    -1.986485523117479485798245416362489554927797880264017876139605e-04,
    -1.535917123534724675069770335876717193700472427021513236587288e-07,
    3.741237880740038181092208138035393952304292615793985030731363e-05,
    -8.520602537446695203919254911655523022437596956226376512305917e-06,
    -3.332634478885821888782452033341036827311505907796498439829337e-06,
    1.768712983627615455876328730755375176412501359114058815453100e-06,
    -7.691632689885176146000152878539598405817397588156525116769908e-08,
    -1.176098767028231698450982356561292561347579777695396953528141e-07,
    3.068835863045174800935478294933975372450179787894574492930570e-08,
    -2.507934454948598267195173183147126731806317144868275819941403e-09
};

static const double db19[38] = {
    1.108669763181710571099154195209715164245299677773435932135455e-03,
    1.428109845076439737439889152950199234745663442163665957870715e-02,
    8.127811326545955065296306784901624839844979971028620366497726e-02,
    2.643884317408967846748100380289426873862377807211920718417385e-01,
    5.244363774646549153360575975484064626044633641048072116393160e-01,
    6.017045491275378948867077135921802620536565639585963293313931e-01,
    2.608949526510388292872456675310528324172673101301907739925213e-01,
    -2.280913942154826463746325776054637207093787237086425909534822e-01,
    -2.858386317558262418545975695028984237217356095588335149922119e-01,
    7.465226970810326636763433111878819005865866149731909656365399e-02,
    2.123497433062784888090608567059824197077074200878839448416908e-01,
    -3.351854190230287868169388418785731506977845075238966819814032e-02,
    -1.427856950387365749779602731626112812998497706152428508627562e-01,
    2.758435062562866875014743520162198655374474596963423080762818e-02,
    8.690675555581223248847645428808443034785208002468192759640352e-02,
    -2.650123625012304089901835843676387361075068017686747808171345e-02,
    -4.567422627723090805645444214295796017938935732115630050880109e-02,
    2.162376740958504713032984257172372354318097067858752542571020e-02,
    1.937554988917612764637094354457999814496885095875825546406963e-02,
    -1.398838867853514163250401235248662521916813867453095836808366e-02,
    -5.866922281012174726584493436054373773814608340808758177372765e-03,
    7.040747367105243153014511207400620109401689897665383078229398e-03,
    7.689543592575483559749139148673955163477947086039406129546422e-04,
    -2.687551800701582003957363855070398636534038920982478290170267e-03,
    3.418086534585957765651657290463808135214214848819517257794031e-04,
    7.358025205054352070260481905397281875183175792779904858189494e-04,
    -2.606761356786280057318315130897522790383939362073563408613547e-04,
    -1.246007917341587753449784408901653990317341413341980904757592e-04,
    8.711270467219922965416862388191128268412933893282083517729443e-05,
    5.105950487073886053049222809934231573687367992106282669389264e-06,
    -1.664017629715494454620677719899198630333675608812018108739144e-05,
    3.010964316296526339695334454725943632645798938162427168851382e-06,
    1.531931476691193063931832381086636031203123032723477463624141e-06,
    -6.862755657769142701883554613486732854452740752771392411758418e-07,
    1.447088298797844542078219863291615420551673574071367834316167e-08,
    4.636937775782604223430857728210948898871748291085962296649320e-08,
    -1.116402067035825816390504769142472586464975799284473682246076e-08,
    8.666848838997619350323013540782124627289742190273059319122840e-10
};

static const double db20[40] = {
    7.799536136668463215861994818889370970510722039232863880031127e-04,
    1.054939462495039832454480973015641498231961468733236691299796e-02,
    6.342378045908151497587346582668785136406523315729666353643372e-02,
    2.199421135513970450080335972537209392121306761010882209298252e-01,
    4.726961853109016963710241465101446230757804141171727845834637e-01,
    6.104932389385938201631515660084201906858628924695448898824748e-01,
    3.615022987393310629195602665268631744967084723079677894136358e-01,
    -1.392120880114838725806970545155530518264944915437808314813582e-01,
    -3.267868004340349674031122837905370666716645587480021744425550e-01,
    -1.672708830907700757517174997304297054003744303620479394006890e-02,
    2.282910508199163229728429126648223086437547237250290835639880e-01,
    3.985024645777120219790581076522174181104027576954427684456660e-02,
    -1.554587507072679559315307870562464374359996091752285157077477e-01,
    -2.471682733861358401587992299169922262915151413349313513685587e-02,
    1.022917191744425578861013681016866083888381385233081516583444e-01,
    5.632246857307435506953246988215209861566800664402785938591145e-03,
    -6.172289962468045973318658334083283558209278762007041823250642e-02,
    5.874681811811826491300679742081997167209743446956901841959711e-03,
    3.229429953076958175885440860617219117564558605035979601073235e-02,
    -8.789324923901561348753650366700695916503030939283830968151332e-03,
    -1.381052613715192007819606423860356590496904285724730356602106e-02,
    6.721627302259456835336850521405425560520025237915708362002910e-03,
    4.420542387045790963058229526673514088808999478115581153468068e-03,
    -3.581494259609622777556169638358238375765194248623891034940330e-03,
    -8.315621728225569192482585199373230956924484221135739973390038e-04,
    1.392559619323136323905254999347967283760544147397530531142397e-03,
    -5.349759843997695051759716377213680036185796059087353172073952e-05,
    -3.851047486992176060650288501475716463266233035937022303649838e-04,
    1.015328897367029050797488785306056522529979267572003990901472e-04,
    6.774280828377729558011184406727978221295796652200819839464354e-05,
    -3.710586183394712864227221271216408416958225264980612822617745e-05,
    -4.376143862183996810373095822528607606900620592585762190542483e-06,
    7.241248287673620102843105877497181565468725757387007139555885e-06,
    -1.011994010018886150340475413756849103197395069431085005709201e-06,
    -6.847079597000556894163334787575159759109091330092963990364192e-07,
    2.633924226270001084129057791994367121555769686616747162262697e-07,
    2.014322023550512694324757845944026047904414136633776958392681e-10,
    -1.814843248299695973210605258227024081458531110762083371310917e-08,
    4.056127055551832766099146230616888024627380574113178257963252e-09,
    -2.998836489619319566407767078372705385732460052685621923178375e-10
};


const double db21[42] = {
    5.488225098526837086776336675992521426750673054588245523834775e-04,
    7.776639052354783754338787398088799862510779059555623704879234e-03,
    4.924777153817727491399853378340056968104483161598320693657954e-02,
    1.813596254403815156260378722764624190931951510708050516519181e-01,
    4.196879449393627730946850609089266339973601543036294871772653e-01,
    6.015060949350038975629880664020955953066542593896126705346122e-01,
    4.445904519276003403643290994523601016151342743089878478478962e-01,
    -3.572291961725529045922914178005307189036762547143966578066838e-02,
    -3.356640895305295094832978867114363069987575282256098351499731e-01,
    -1.123970715684509813515004981340306901641824212464197973490295e-01,
    2.115645276808723923846781645238468659430862736248896128529373e-01,
    1.152332984396871041993434411681730428103160016594558944687967e-01,
    -1.399404249325472249247758764839776903226503657502071670245304e-01,
    -8.177594298086382887387303634193790542522570670234556157566786e-02,
    9.660039032372422070232189700372539681627783322249829842275517e-02,
    4.572340574922879239251202944731235421034828710753381191345186e-02,
    -6.497750489373232063332311106008616685748929419452249544690967e-02,
    -1.865385920211851534093244412008141266131208093007217139232170e-02,
    3.972683542785044175197464400756126818299918992482587866999707e-02,
    3.357756390338110842532604766376200760791669954106679933144723e-03,
    -2.089205367797907948785235479746212371728219866525211135343707e-02,
    2.403470920805434762380632169785689545910525667396313550679652e-03,
    8.988824381971911875349463398395464114417817949738911101372312e-03,
    -2.891334348588901247375268718015882610844675931117463495551958e-03,
    -2.958374038932831280750770228215510959830170264176955719827510e-03,
    1.716607040630624138494506282569230126333308533535502799235333e-03,
    6.394185005120302146432543767052865436099994387647359452249347e-04,
    -6.906711170821016507268939228893784790518270744313525548714065e-04,
    -3.196406277680437193708834220804640347636984901270948088339102e-05,
    1.936646504165080615323696689856004910579777568504218782029027e-04,
    -3.635520250086338309442855006186370752206331429871136596927137e-05,
    -3.499665984987447953974079490046597240276268044409625722689849e-05,
    1.535482509276049283124233498646050472096482329299719141107128e-05,
    2.790330539814487046106169582691767916283793946025922387556917e-06,
    -3.090017164545699197158555936852697325985864588418167982685400e-06,
    3.166095442367030556603889009833954440058545355777781782000278e-07,
    2.992136630464852794401294607536813682771292352506328096125857e-07,
    -1.000400879030597332045460600516621971679363965166249211063755e-07,
    -2.254014974673330131563184851456825991617915549643308754828159e-09,
    7.058033541231121859020947976903904685464512825731230495144226e-09,
    -1.471954197650365265189549600816698778213247061389470277337173e-09,
    1.038805571023706553035373138760372703492942617518816122570050e-10 };

const double db22[44] = {
    3.862632314910982158524358900615460368877852009576899680767316e-04,
    5.721854631334539120809783403484493333555361591386208129183833e-03,
    3.806993723641108494769873046391825574447727068953448390456335e-02,
    1.483675408901114285014404448710249837385836373168215616427030e-01,
    3.677286834460374788614690818452372827430535649696462720334897e-01,
    5.784327310095244271421181831735444106385099957908657145590104e-01,
    5.079010906221639018391523325390716836568713192498711562711282e-01,
    7.372450118363015165570139016530653113725172412104955350368114e-02,
    -3.127265804282961918033226222621788537078452535993545440716988e-01,
    -2.005684061048870939324361244042200174132905844868237447130382e-01,
    1.640931881067664818606223226286885712554385317412228836705888e-01,
    1.799731879928913037252154295313083168387840791424988422757762e-01,
    -9.711079840911470969274209179691733251456735137994201552926799e-02,
    -1.317681376866834107513648518146838345477875022352088357523838e-01,
    6.807631439273221556739202147004580559367442550641388181886023e-02,
    8.455737636682607503362813659356786494357635805197410905877078e-02,
    -5.136425429744413245727949984018884707909441768477091944584584e-02,
    -4.653081182750671347875833607846979997825771277976548080904423e-02,
    3.697084662069802057615318892988581825637896696876361343354380e-02,
    2.058670762756536044060249710676656807281671451609632981487139e-02,
    -2.348000134449318868560142854519364987363882333754753819791381e-02,
    -6.213782849364658499069336123807608293122900450508440420104462e-03,
    1.256472521834337406887017835495604463815382993214296088172221e-02,
    3.001373985076435951229129255588255746904937042979316054485183e-04,
    -5.455691986156717076595353163071679107868762395367234726592273e-03,
    1.044260739186025323350755659184734060807432172611689413745029e-03,
    1.827010495657279080112597436850157110235336772062961041154607e-03,
    -7.706909881231196232880372722955519781655769913634565757339739e-04,
    -4.237873998391800799531947768003976978197438302533528661825758e-04,
    3.286094142136787341983758471405935405823323072829619248523697e-04,
    4.345899904532003379046992625575076092823809665933575578710696e-05,
    -9.405223634815760421845190098352673647881298980040512091599943e-05,
    1.137434966212593172736144274866639210339820203135670505287250e-05,
    1.737375695756189356163565074505405906859746605867772002320509e-05,
    -6.166729316467578372152251668422979152169587307212708981768966e-06,
    -1.565179131995160159307426993578204733378112742579926503832095e-06,
    1.295182057318877573889711232345068147800395721925682566394936e-06,
    -8.779879873361286276888117046153049053917243760475816789226764e-08,
    -1.283336228751754417819693932114064887075096030264748079976736e-07,
    3.761228749337362366156711648187743399164239397803629022612862e-08,
    1.680171404922988885554331183691280245962290247654438114807112e-09,
    -2.729623146632976083449327361739104754443221903317745768938846e-09,
    5.335938821667489905169783227036804533253011117886586305435615e-10,
    -3.602113484339554703794807810939301847299106970237814334104274e-11 };

const double db23[46] = {
    2.719041941282888414192673609703302357098336003920923958924757e-04,
    4.202748893183833538390034372523511472345215563611003407984701e-03,
    2.931000365788411514736204018929480427874317460676079959515131e-02,
    1.205155317839719336306053895611899089004274336891709067958035e-01,
    3.184508138528652363416527748460472152790575031409830417259640e-01,
    5.449311478735204282674240672421984387504149924834544495466793e-01,
    5.510185172419193913452724227212507720514144116478727269717859e-01,
    1.813926253638400136259098302138614937264260737638175539416540e-01,
    -2.613921480306441118856795735210118413900307577511142987337375e-01,
    -2.714020986078430556604069575184718123763697177381058877113471e-01,
    9.212540708241805260646030910734894258577648089100630012130261e-02,
    2.235736582420402317149513960822561717689875252792817094811874e-01,
    -3.303744709428937875006612792463031409461636228731285046551636e-02,
    -1.640113215318759250156057837165276039181451149292112929401186e-01,
    2.028307457564929974897286607551313323418860610791382310375731e-02,
    1.122970436181072886950734465075645977754665593869789965874572e-01,
    -2.112621235622724100704783293549467048999443844657058425212982e-02,
    -7.020739157490110946204219011957565343899895499962369353294028e-02,
    2.176585683449997560776882472168730165799461445156766923497545e-02,
    3.849533252256919901057154320407596073180564628069920893870768e-02,
    -1.852351365015615979794689960740674782817814176166333519597796e-02,
    -1.753710100303584537915846117408613551147985251726558719415169e-02,
    1.275194393152828646243157404474947115052750581861997731041018e-02,
    6.031840650024162816289878206037841640814102314209075233751820e-03,
    -7.075319273706152814194039481466556204493276773483821748740018e-03,
    -1.134865473356251691289337120013286756337393784110786907825400e-03,
    3.122876449818144997419144765125750522437659393621577492535411e-03,
    -2.465014005163512031940473100375377210862560761576109755841161e-04,
    -1.061231228886651321139357625683805642193648671030425010215075e-03,
    3.194204927099011503676530359692366990929679170022583007683112e-04,
    2.567624520078737205563856675376636092314813400664190770435450e-04,
    -1.500218503490340967673163290447832236259277810659068637402668e-04,
    -3.378894834120903434270962452674534330903724108906662510305045e-05,
    4.426071203109246077621875303440935335701832843654692827539837e-05,
    -2.635207889249186237209225933170897825432335273771458456888097e-06,
    -8.347875567854625544366043748844183086765894974439245409223337e-06,
    2.397569546840240057403739507525641239509517148980849889986407e-06,
    8.147574834779447778085443041422881439860288287528356019216814e-07,
    -5.339005405209421154584783682848780965053642859373536945701365e-07,
    1.853091785633965019353699857864654181728710556702529908304185e-08,
    5.417549179539278736503176166323685597634496102979977037271945e-08,
    -1.399935495437998845130909687361847103274208993447892120341999e-08,
    -9.472885901812050535221582074673490573092096712822067564903012e-10,
    1.050446453696543404071105111096438573423068913105255997908040e-09,
    -1.932405111313417542192651899622541612314066389643607507706887e-10,
    1.250203302351040941433216718217504240541423430995137507404787e-11 };

const double db24[48] = {
    1.914358009475513695026138336474115599435172088053846745168462e-04,
    3.082081714905494436206199424544404720984720556128685270556458e-03,
    2.248233994971641072358415157184825628226776692231940577581580e-02,
    9.726223583362519663806545734008355914527504417674578571164300e-02,
    2.729089160677263268706137134412557268751671263458895098625356e-01,
    5.043710408399249919771876890402814109246866444441814540282099e-01,
    5.749392210955419968460807901923407033144945935105622912839838e-01,
    2.809855532337118833442626085115402941842959475929278883281409e-01,
    -1.872714068851562376981887159775791469060265778441667840307934e-01,
    -3.179430789993627375453948489797707550898087789160025182664299e-01,
    4.776613684344728187950198323031360866349104994035553200788631e-03,
    2.392373887803108551973268291945824822214858134512317715815616e-01,
    4.252872964148383258147364472170645232684343235486951540533893e-02,
    -1.711753513703468896897638515080572393949165942335556397917666e-01,
    -3.877717357792001620177594726199572688446488033750771020190283e-02,
    1.210163034692242362312637311149062286659377039046006801523826e-01,
    2.098011370914481534980883827326017063121637262728447783605518e-02,
    -8.216165420800166702291466006164189460916816748629968198028898e-02,
    -4.578436241819221637997516339765068825260159169893967894877272e-03,
    5.130162003998087915555334881398688958843078494595140394873884e-02,
    -4.944709428125628299815920032649550811877887219282751174798211e-03,
    -2.821310709490189098113895361900699228886900995412759197674058e-02,
    7.661721881646585897329899904308764405384658404613669817843430e-03,
    1.304997087108573583052494067883717533043101857128653233783396e-02,
    -6.291435370018187780721843581169343900864298634085743861509767e-03,
    -4.746568786323113800477796959513558401732252800905982385017245e-03,
    3.736046178282523345179052160810332868725126356493155728625572e-03,
    1.153764936839481504858282495202271984454410046682805375157566e-03,
    -1.696456818974824394274534636412116243080312601322325642741589e-03,
    -4.416184856141520063365958900079406737636243682138363561877750e-05,
    5.861270593183109933716735450272894035425792347806515678695765e-04,
    -1.181233237969554740613021227756568966806892308457221016257961e-04,
    -1.460079817762616838924301818082729036314539476811023255670666e-04,
    6.559388639305634085303738560455061974369354538271316071502698e-05,
    2.183241460466558363365044032984257709791187640963509380549307e-05,
    -2.022888292612697682860859987200455702614855595412267510558659e-05,
    1.341157750809114719319937553186023660581084151828593222893663e-08,
    3.901100338597702610409014129024223853127911530009766793352492e-06,
    -8.980253143938407724149926669980791166378388013293887718404796e-07,
    -4.032507756879971624098983247358983425236092110387724315244646e-07,
    2.166339653278574639176393978510246335478946697396400359281412e-07,
    -5.057645419792500308492508924343248979317507866520688417567606e-10,
    -2.255740388176086107368821674947175804005323153443170526520277e-08,
    5.157776789671999638950774266313208715015419699643333784626363e-09,
    4.748375824256231118094453549799175824526559994333227456737433e-10,
    -4.024658644584379774251499574468195118601698713554294941756559e-10,
    6.991801157638230974132696433509625934021677793453732225542951e-11,
    -4.342782503803710247259037552886749457951053124203814185811297e-12 };

const double db25[50] = {
    1.348029793470188994578489247159356055370460656508881471268611e-04,
    2.256959591854779520121391049628056149270016860666661928130747e-03,
    1.718674125404015533817186914954848902241194002444696221013131e-02,
    7.803586287213267559750659320481403668422052199257139168386084e-02,
    2.316935078860218199900621518057089104946216881512075361624214e-01,
    4.596834151460945937896973864539659944010260858049947396093277e-01,
    5.816368967460577833534892038757085635755639698734580573323031e-01,
    3.678850748029466984371319740855532278670733841012809062966976e-01,
    -9.717464096463814276130048169040892607068486428294030952842447e-02,
    -3.364730796417461309562110148848845218930261030262170601615289e-01,
    -8.758761458765466140226687673880006154266689569025041229545538e-02,
    2.245378197451017129525176510409543155930843160711989062118482e-01,
    1.181552867199598604563067876819931882639429216001523151773895e-01,
    -1.505602137505796309518094206831433270850173484773520730186277e-01,
    -9.850861528996022153725952822686729410420350758543226219234795e-02,
    1.066338050184779528831274540522414711301747903916268438037723e-01,
    6.675216449401860666895983072443984697329752470942906490126865e-02,
    -7.708411105657419356208567671699032054872853174701595359329826e-02,
    -3.717396286112250887598137324046870459877639250821705817221557e-02,
    5.361790939877949960629041419546536897037332284703545849594129e-02,
    1.554260592910229163981295854603203625062268043511894295387375e-02,
    -3.404232046065334099320628584033729153497903561399447916116575e-02,
    -3.079836794847036661636693963570288706232460663070983852354326e-03,
    1.892280447662762841086581178691039363674755753459524525886478e-02,
    -1.989425782202736494289461896386235348901617760816745484282494e-03,
    -8.860702618046368399013064252456556969199612331833605310278698e-03,
    2.726936258738495739871469244610042793734119359765762028996059e-03,
    3.322707773973191780118197357194829286271392998979276105842863e-03,
    -1.842484290203331280837780430014195744813667655929909114672154e-03,
    -8.999774237462950491085382524008429604309720852269895692000702e-04,
    8.772581936748274843488806190175921376284150686011179612908221e-04,
    1.153212440466300456460181455345639872216326644527860903202733e-04,
    -3.098800990984697989530544245356271119416614147098459162436317e-04,
    3.543714523276059005284289830559259809540337561365927850248007e-05,
    7.904640003965528255137496303166001735463107762646364003487560e-05,
    -2.733048119960041746353244004225286857636045649642652816856524e-05,
    -1.277195293199783804144903848434605690990373526086311486716394e-05,
    8.990661393062588905369930197413951232059323587543226269327396e-06,
    5.232827708153076417963912065899772684403904504491727061662335e-07,
    -1.779201332653634562565948556039009149458987774189389221295909e-06,
    3.212037518862519094895005816661093988294166712919881121802831e-07,
    1.922806790142371601278104244711267420759978799176017569693322e-07,
    -8.656941732278507163388031517930974947984281611717187862530250e-08,
    -2.611598556111770864259843089151782206922842627174274274741722e-09,
    9.279224480081372372250073354726511359667401736947170444723772e-09,
    -1.880415755062155537197782595740975189878162661203102565611681e-09,
    -2.228474910228168899314793352064795957306403503495743572518755e-10,
    1.535901570162657197021927739530721955859277615795931442682785e-10,
    -2.527625163465644811048864286169758128142169484216932624854015e-11,
    1.509692082823910867903367712096001664979004526477422347957324e-12 };

const double db26[52] = {
    9.493795750710592117802731381148054398461637804818126397577999e-05,
    1.650520233532988247022384885622071050555268137055829216839523e-03,
    1.309755429255850082057770240106799154079932963479202407364818e-02,
    6.227474402514960484193581705107415937690538641013309745983962e-02,
    1.950394387167700994245891508369324694703820522489789125908612e-01,
    4.132929622783563686116108686666547082846741228042232731476147e-01,
    5.736690430342222603195557147853022060758392664086633396520345e-01,
    4.391583117891662321931477565794105633815363384084590559889493e-01,
    1.774076780986685727823533562031556893226571319881417676492595e-03,
    -3.263845936917800216385340830055349953447745005769416287177497e-01,
    -1.748399612893925042664835683606584215248582345438816346170042e-01,
    1.812918323111226960705459766025430918716233584167982942044424e-01,
    1.827554095896723746537533832033286839689931924709760567945595e-01,
    -1.043239002859270439148009137202747658420968144330108510179290e-01,
    -1.479771932752544935782314546369458188243947772922980064071205e-01,
    6.982318611329236513756591683950208955110603212379412334701145e-02,
    1.064824052498086303236593797715344405836015002929319291715777e-01,
    -5.344856168148319149493577269390074213960237013099439431132086e-02,
    -6.865475960403591525454725258715351280947435823354011140858001e-02,
    4.223218579637203541206570902753288247790857760067894456114927e-02,
    3.853571597111186425832144567362328142994885395255438867968781e-02,
    -3.137811036306775484244644776337594435094096964336402798072360e-02,
    -1.776090356835818354094298625884058170354129044259951019182732e-02,
    2.073492017996382475887790073068984224515077665517103399898854e-02,
    5.829580555318887971939315747596613038479561943085291072787359e-03,
    -1.178549790619302893728624468402138072504226527540325463847390e-02,
    -5.287383992626814439198630765217969804966319971038003993984480e-04,
    5.601947239423804853206514239940474788977188460452053462770324e-03,
    -9.390582504738289646165698675070641765810790863514339205205998e-04,
    -2.145530281567620980305401403432221668847980295600748913748902e-03,
    8.383488056543616046381924054554052104937784379435436426690560e-04,
    6.161382204574344193703789012696411561214682388271673214197731e-04,
    -4.319557074261807466712901913481943478521991611607433971794602e-04,
    -1.060574748283803889966150803551837402553866816191659959347053e-04,
    1.574795238607493590547765666590811258087715699737771458390360e-04,
    -5.277795493037868976293566636015627609248847457646525246271036e-06,
    -4.109673996391477816326502438997466532822639385119090230965252e-05,
    1.074221540872195031273584409245060623104931330938273936484593e-05,
    7.000078682964986734859102495210684809643657474253921074934684e-06,
    -3.887400161856795187587790410706550576033603097954065074023128e-06,
    -4.650463220640262639231145944536092973446596027469833860001618e-07,
    7.939210633709952088373459255067360793370284788682979065122810e-07,
    -1.079004237578671411922961583845716126060658213943840375162654e-07,
    -8.904466370168590769052983362721567202750591914741016835071257e-08,
    3.407795621290730008673832107214820587991557116806912418558069e-08,
    2.169328259850323106986222296525930099935873861026310788086221e-09,
    -3.776010478532324328184043667556576385639846460337894963138621e-09,
    6.780047245828636668305808192607091517605349478677442468580825e-10,
    1.002303191046526913509281844136258004034177309673269533418644e-10,
    -5.840408185341171468465492447799819262905317576847426970757700e-11,
    9.130510016371796243923232926650252570239054815939483900056681e-12,
    -5.251871224244435037810503452564279828539007071678724285717464e-13 };

const double db27[54] = {
    6.687131385431931734918880680779563307675740731544063787599480e-05,
    1.205531231673213234251999812212394463872002561229330125152073e-03,
    9.952588780876619771874091297340545740163119816300838847749336e-03,
    4.945259998290488004302995584228917712171023349013386944893643e-02,
    1.629220275023933206396286389387812803673796872000118325233533e-01,
    3.671102141253898226423388094379126394383458407087000700420400e-01,
    5.538498609904800487605460395549044755068663194750017660900436e-01,
    4.934061226779989979265447084358038959373468583404767251300717e-01,
    1.028408550618229112710739475157388764479351682549490307668477e-01,
    -2.897168033145948463175311101489473923261698802610323264603418e-01,
    -2.482645819032605667810198368127693701263349361209208170092197e-01,
    1.148230195177853576326445213787661879970642975306605349249036e-01,
    2.272732884141708265275037216925482827043581894357907763081103e-01,
    -3.878641863180231062443346843661817078060143110529946543683356e-02,
    -1.780317409590085821070366277249759321269342801053489323888575e-01,
    1.579939746024048431173907799261019471878724997312653292884660e-02,
    1.311979717171553289711406975836688896451835867594492827800969e-01,
    -1.406275155580876537026622167053147161846397735962817855782362e-02,
    -9.102290652956591798241345515773322449830692586525337562864481e-02,
    1.731101826549371089085675445961947677452358872325373949295769e-02,
    5.796940573471798814748840657698008349462526768238833307489106e-02,
    -1.851249356199807710545837861298826718763077900221574749342712e-02,
    -3.273906663102087145481936428049519742538150452785563039743756e-02,
    1.614696692239566682272152627542980896527822528487665111124260e-02,
    1.566559564892457873003263983940819950829497022298967052103291e-02,
    -1.157718645897628140054089958116866381056430680879332334217267e-02,
    -5.862096345462925972966025215266179082657169806555503857975278e-03,
    6.856635609684880675273184141746359000591385833807880272568038e-03,
    1.342626877303679609082208800217479591902967766971379107017011e-03,
    -3.332854469520006162763300141047111065412307706449049389557931e-03,
    1.457529625931728587128588244152604734177322144376309490881599e-04,
    1.301177450244135139135787970279897042994109161268159963884641e-03,
    -3.418351226915427611946547437228006377896519777431057005796358e-04,
    -3.879018574101327604369144470124819695479087900682219330965466e-04,
    2.019719879690326857104208791272390315160018069955787875123234e-04,
    7.660058387068576876674274961751262847965101108848090019821555e-05,
    -7.711145517797584208411720507329584053382646435270054267102827e-05,
    -3.517483614907445391752737841583832374184046409747387149129674e-06,
    2.063442647736885318487206413360228908558806028468062177953960e-05,
    -3.901164070638425528170558032557368703418425915665413541985623e-06,
    -3.657500908187104997045760131046655906827644494899206692043298e-06,
    1.634369624725637835424610743915128591988676092276368687669255e-06,
    3.050880686251999094242671997731089918322345713516567387655763e-07,
    -3.472468147394389269364673179891460601330730511237974736379548e-07,
    3.286558968055159530983261866450459360074591641809187825408848e-08,
    4.026255052866908637178682747490340533992340623231336911661711e-08,
    -1.321332273990056558848617809101876846857728483295631388083263e-08,
    -1.309465606856955151282041809232358209226373823424148862843577e-09,
    1.521614984778521740775073159445241799352681846880808663329946e-09,
    -2.415526928011130660506395791946234018673860470542996426005750e-10,
    -4.374986224293654395069947682013996351823060759948583134078918e-11,
    2.213662088067662485181472969374945928903854605356443772873438e-11,
    -3.295790122476585807069953975043096139541415768606924980926275e-12,
    1.828188352882424933624530026056448539377272017834175009418822e-13 };

const double db28[56] = {
    4.710807775014051101066545468288837625869263629358873937759173e-05,
    8.794985159843870273564636742144073059158975665525081816488582e-04,
    7.542650377646859177160195786201116927568410621050693986450538e-03,
    3.909260811540534426092083794403768111329778710541126982205076e-02,
    1.351379142536410450770749411679708279921694061092200363031937e-01,
    3.225633612855224257318486139030596702170126503618082416187649e-01,
    5.249982316303355562348293243640252929543774162151269406404636e-01,
    5.305162934414858075256978195354516449402692654391295761050628e-01,
    2.001761440459844380384404537971725815970574972480152145882083e-01,
    -2.304989540475825257279397658067038304888129374484095837624889e-01,
    -3.013278095326417816909366061441334075444383937588485826752087e-01,
    3.285787916338710468450547883547348694255260871071954509422161e-02,
    2.458081513737595535752949960866466132239832334168533456626848e-01,
    3.690688531571127205290633425993077868843846977265847006108551e-02,
    -1.828773307329849166920408764650763092868965221608724574218473e-01,
    -4.683823374455167616514752420549419665215987106243491879971921e-02,
    1.346275679102260877490923315484152662987698625205479167761416e-01,
    3.447863127509970524678534595639646616244376966117385829345554e-02,
    -9.768535580565244174963692133038973587005628990493154911133358e-02,
    -1.734192283130589908795581592406238282930530566316914040035812e-02,
    6.774789550190933956165341752699717255041141690153626336867769e-02,
    3.448018955540951137600471926079622335842207388713342609755316e-03,
    -4.333336861608628393863254980828284403766309203453808666888800e-02,
    4.431732910062988320487418656322338284504389482966303454010563e-03,
    2.468806001015186586264188361362046240243934625858343309818244e-02,
    -6.815549764552309639259447104811254179605050667281644254737890e-03,
    -1.206359196821849005842466619530619474644989878503490321948471e-02,
    5.838816627748944864497370576838809711476027837762897602935327e-03,
    4.784863112454241718009916669120329848973107781600157214960003e-03,
    -3.725461247074254799171427871442937099025589672466088044410521e-03,
    -1.360373845639692436577650137133777929659265166644839235882291e-03,
    1.875998668202795626152766912508562385106168761893900192731562e-03,
    1.415672393140464257573780581396205840941849282748250523509874e-04,
    -7.486749559114629991320679819683227355746847370960399216568306e-04,
    1.154656063658921251969297916771881248142872975490882572741198e-04,
    2.295790982233456202366621544054366855729175050420515776344878e-04,
    -8.903901490044488099517361247378396756893227855233897357882978e-05,
    -4.907713416190250858324783990436748073854807494400738311968278e-05,
    3.641401211050802781223450761733180188911730291497201507086247e-05,
    4.638664981394294654002871426476885751050837817671843706915388e-06,
    -1.004326041333422601781848560432120920634648692782357855473103e-05,
    1.247900317574834146052381692752796047052443265982232422642017e-06,
    1.840363734517769191684379309039277810350620305330900536404818e-06,
    -6.670215479954892588747450458085225880096882699397256774967304e-07,
    -1.757461173209842779903676264971918635870906983281392939812547e-07,
    1.490660013535362170989340065033061951960933954388633507264360e-07,
    -8.262387315626556965966429243600984899650039704831080988658278e-09,
    -1.784138690875710077191713941441263246560738410213624546116655e-08,
    5.044047056383436444631252840057862002264087720676808580373667e-09,
    6.944540328946226952976704718677697525410051405055662575530111e-10,
    -6.077041247229010224760245305596307803830053533836849384680534e-10,
    8.492220011056382105461206077240377024404404638947591299761197e-11,
    1.867367263783390418963879146175452376940453585791428841004699e-11,
    -8.365490471258800799349289794397908900767054085216008197372193e-12,
    1.188850533405901520842321749021089497203940688882364518455403e-12,
    -6.367772354714857335632692092267254266368934590973693820942617e-14 };

const double db29[58] = {
    3.318966279841524761813546359818075441349169975922439988843475e-05,
    6.409516803044434540833706729120596322083061716935004987374676e-04,
    5.702126517773375434760843998623507494914551464968126455168657e-03,
    3.077358022140837676716707336516751814713312018344719150923618e-02,
    1.113701169517405304762186166370327770191325772342190715118617e-01,
    2.806534559709829376968881262770480606500920092398534229615289e-01,
    4.897588047621993143592705932993573539235839610055331620240518e-01,
    5.513744327583751951223746071670135992466984391233429663886536e-01,
    2.891052383358291634605691113586264061513180158354460952469246e-01,
    -1.540287344599000542466293779503370141731339982919280951230240e-01,
    -3.300409489175880520295083779487012611959310539629627124613719e-01,
    -5.570680007294085781514541931715795784309410235726214400350351e-02,
    2.361052361530259415983110734054626770649468357328362426830433e-01,
    1.124191748731883764769740670535880543076817816861518667898467e-01,
    -1.608779885941877360771615465531852333085159940159968393590303e-01,
    -1.078459499387214201077881957354707913786241153934264316589273e-01,
    1.144722958938182579734135930060053286267822797640393386903440e-01,
    8.322074716244975790297348835032537357891920536002627784941129e-02,
    -8.512549261563550232832311331420804581881235448862834507281486e-02,
    -5.502748952532572320924541450626650067707344725344841099873446e-02,
    6.347916458421186633577789314698972361081611994794140119302163e-02,
    3.053154327270413646637328212093941030592133225231728964047047e-02,
    -4.518798127778834515979704475304405691390090327474972089790857e-02,
    -1.291714255426679462966473962555410660387671182428076570686472e-02,
    2.947043187174764111028122319949903667638786379520519899154373e-02,
    2.648327307678167915542397563479749119673768286990136051577167e-03,
    -1.704122457360668969234196743407615179099529206118693044741086e-02,
    1.737880332720511164430027824345354801611373419264590068097416e-03,
    8.469725493560752287772961661104710791306496373354237126998903e-03,
    -2.550807127789472659145072247724735637183590942511858255354005e-03,
    -3.473798989681100630649790255076233970957721666820195620598374e-03,
    1.877120925723650133179338154344873477230567340668548016358682e-03,
    1.087053942226062966738944397844498417945523630053411148182206e-03,
    -1.000778327085680541055696707760062870925897014530348262794137e-03,
    -2.000711363076779808296301110796026470163110202848894744316755e-04,
    4.111283454742767033424740543004041500054889660665367490129376e-04,
    -2.292018041214499897382298271438084577065170236103859181134525e-05,
    -1.293044840080720609161466939678226852440475312744714379499074e-04,
    3.645026068562774967665464216602750761690984830805534178557146e-05,
    2.913344750169041218495787251929571015775436967652945386217480e-05,
    -1.657328395306616289863396387854880512976861409870690029695161e-05,
    -3.593644804025187638066915189731950450034629392522542962477168e-06,
    4.750609246452552850197117564759363194953518317428400241629683e-06,
    -3.029054592052818286474228294307141792053791695855058563299597e-07,
    -8.975701750636280734511651941681818767895052287332471537510510e-07,
    2.633898386997696553900967704111473475368019612368922599394214e-07,
    9.387197411095863026484410601284876812292554863800653292318725e-08,
    -6.286156922010786166768503252870590953166867739448102804392389e-08,
    1.076591906619196137385201975028785139607670319821266803566785e-09,
    7.768978854770062238895964639391324551611701293594055935346266e-09,
    -1.893995386171984147774611076618946011337498790609031626697228e-09,
    -3.426800863263089001811012278889864200550342566386405676893537e-10,
    2.407099453509342962399811991929330725186626582891090462239366e-10,
    -2.940589250764532582888473974638273664244682541297835986306504e-11,
    -7.832509733627817032356556582819494794884131433810848844709881e-12,
    3.152762413370310423797539876893861621418382024668704492620948e-12,
    -4.285654870068344101898185073376307686875386259541180967347399e-13,
    2.219191311588302960934661700068023727737812918006011019184982e-14 };

const double db30[60] = {
    2.338616172731421471474407279894891960011661146356580425400538e-05,
    4.666379504285509336662000111055365140848987563882199035322085e-04,
    4.300797165048069510045016757402827408493482974782286966500398e-03,
    2.413083267158837895194919987958311943976725005113561262334092e-02,
    9.123830406701570679321575555085899708564500191080751595642650e-02,
    2.420206709402140994467599658342919512318194032687898436229538e-01,
    4.504878218533178366981351802898336415314944375740699506554771e-01,
    5.575722329128364304078082520999850413492571645754785374629734e-01,
    3.662426833716279793144871151369089533016299234992584741629624e-01,
    -6.618367077593731501909741041813726474911212544474895441395148e-02,
    -3.329669750208556069196849320598850505877494561268613506392514e-01,
    -1.419685133300829310219026267403758254954270602825020111483505e-01,
    1.994621215806643032428990062111230223523226088131364328774921e-01,
    1.778298732448367361280250921330425046260289700971176750362566e-01,
    -1.145582194327077814891518778613672243404957549114393749173137e-01,
    -1.572368179599938126878197378886501553251711910617673398124611e-01,
    7.277865897036442699893544326605244235248713804556715604416632e-02,
    1.227477460450093778691578797698150091624353365248212907325446e-01,
    -5.380646545825707676022015051837304300338645984615639237930800e-02,
    -8.765869003638366048026572053699028353846982304851342479893827e-02,
    4.380166467141773250305407710250135373016604593736480428415303e-02,
    5.671236574473569492590636983030617493807140224924978946302257e-02,
    -3.567339749675960965780819743176056734137251336781389369397564e-02,
    -3.226375891935220815954913483392725682165778426411705216010280e-02,
    2.707861959529418272206848318420006522973840949600186710327776e-02,
    1.528796076985739546052896626042375110302102640936712142026221e-02,
    -1.839974386811734118728169880549148389603890445324127330811811e-02,
    -5.296859666131086629169938675330494864053932988161015674773617e-03,
    1.091563165830488927536881480211929049886878831313700460017968e-02,
    6.196717564977244383592534999284255315694546230739551683085460e-04,
    -5.530730148192003288871383856487027893918513053091795443517653e-03,
    8.433845866620933982126003584365932145598126087481400294999080e-04,
    2.324520094060099304385756339638431339131122661576649123053845e-03,
    -8.609276968110423879660725173525347077801305237644122054954659e-04,
    -7.678782504380918697963922441514742758516706160788123977340073e-04,
    5.050948239033467796256544554086554367969638627715114003635557e-04,
    1.724825842351709725545759714374272164367933578194910678479473e-04,
    -2.161718301169633804271038862087964094429005266172702380483361e-04,
    -8.548305467584070994787824796256108217987765582429940610377190e-06,
    6.982008370808327851082027193100914402221658444151889697045071e-05,
    -1.339716863293971629296314599448901465078920406443516550195793e-05,
    -1.636152478725426488654528710478856195004608401773950511915162e-05,
    7.252145535890469015723401169934327900622894130695550273452916e-06,
    2.327549098493686509557358103785598216688723737824121617676858e-06,
    -2.187267676996166416699555236143059249832615777542412142603694e-06,
    1.099474338526203304286307383463498542376432972308342428764576e-08,
    4.261662326011572446469849114416378817419458434583398455985144e-07,
    -1.000414682354500898864979332965559934104686157639553850670490e-07,
    -4.764379965139453357729154748688006975561934425368712852985388e-08,
    2.605442754977625431940885841950955928085338672381046225838880e-08,
    5.553397861397053982967618072672572206490972606026556946910028e-10,
    -3.331105680467578245901976412732595596538702049437802824373020e-09,
    6.984862691832182584221096665570313611280449991512869846064780e-10,
    1.613622978270904360610418704685783656905979134344922647926295e-10,
    -9.461387997276802120884525814092001871993910062127702293573920e-11,
    1.000105131393171192746337860330428369495110180346654025287492e-11,
    3.239428638532286114355931428908079297696045600279108835760520e-12,
    -1.185237592101582328254231496310584611948560976394420324137742e-12,
    1.543997570847620046003616417646988780670333040868954794039905e-13,
    -7.737942630954405708679963277418806436871098329050829841696327e-15 };

const double db31[62] = {
    1.648013386456140748122177817418358316441195236228590958603489e-05,
    3.394122037769956699157160165352942212213928231154233571163033e-04,
    3.236884068627721221829662672296912258338131668810067169630813e-03,
    1.885369161298591269159568944275763468999829139547989648553486e-02,
    7.433609301164788697908776495388047669378919816041031344650271e-02,
    2.070128744852353286198055444111916450619762837756134323019573e-01,
    4.091922000374278563928213235836188963704298775635493549519369e-01,
    5.511398409142754983590484577074663132074992263886810324421617e-01,
    4.294688082061372955430413148799008354573408538414331312236645e-01,
    2.716921249736946422305354732634261873401679092095992827198308e-02,
    -3.109551183195075186926560285811004715398678229333522634202008e-01,
    -2.179784855235633521693544507220105631639547435903112747133934e-01,
    1.401782887652732681656253206993073895422881511380152633441096e-01,
    2.249667114737370933697297905066886078307490136415302624018330e-01,
    -4.992634916046823977000579399730138693074543903234092797936484e-02,
    -1.869623608957154494374577196258383009208655076187653847079167e-01,
    1.543698842948893409652995335281236231845293548571166883219023e-02,
    1.450895009319931981518942907854879059128872873116921504156674e-01,
    -8.139832273469236863527708715566588550006680549152344840146851e-03,
    -1.076127733234956326668605511648013952380301953590447106075614e-01,
    1.094129745236496925725237900637802669504835743555466811796369e-02,
    7.535361174328140695528289751109133941376701984419452638686226e-02,
    -1.488002661810482202699555987503429289100801979910046913257306e-02,
    -4.861907546485433003537603385831190109391263542044516048871113e-02,
    1.615417156598591113619453864586701665635869166193865651960591e-02,
    2.804761936675616906861927211659154977049392281479113764697785e-02,
    -1.427627527776351943309800140756746087215016194775579070599004e-02,
    -1.390055293926652880755898888934447671732373519028670201124816e-02,
    1.051763948737184089128633441244991643331033825102031908858652e-02,
    5.516163573310992566561289762241160214476622662764637181816550e-03,
    -6.520852375874612553325469682628530079210293774541131381751695e-03,
    -1.428264223218909891400516038687842292177211292295049238921068e-03,
    3.393066776715931928419358796960612411097347419792355896915546e-03,
    -6.397901106014600492881202314307290077992972755016494062875201e-05,
    -1.459041741985160943114515221598080223845239255190055621901681e-03,
    3.431398296904734438118401084929505912208229684629857530009147e-04,
    4.998816175637222614896912406679513231966722440032799024979502e-04,
    -2.396583469402949615285646688069476140260781708006174912535660e-04,
    -1.243411617250228669409179807383399199879641177993453588807726e-04,
    1.089584350416766882738651833752634206358441308880869184416670e-04,
    1.501335727444532997071651937630983442758297688087711521441229e-05,
    -3.631255157860086164261313773172162991107348698083164489165837e-05,
    4.034520235184278839752741499546098778993926344831736074409765e-06,
    8.795301342692987765440618030678349427367022581211855857458220e-06,
    -3.035142365891509630069007852947057220760887215249503512783023e-06,
    -1.369060230942940782050489751987123955074404782177163471279285e-06,
    9.810015422044371573950976088058064384946146188110905321673802e-07,
    5.327250656974915426977440959783080593776012130063170688309127e-08,
    -1.975925129170206248152121156696590501303803187231928513867046e-07,
    3.616826517331004805247567218405798591329788122337274956172315e-08,
    2.328309713821409644308538888589329921141948539678106680777082e-08,
    -1.061529602150252306500404266150823962402673780484965538270541e-08,
    -6.474311687959861398702581539341954438747926255671605657095807e-10,
    1.408568151025177427076547804944585301332087108125727813194374e-09,
    -2.524043954153353306183643702933218308617979467184848456565837e-10,
    -7.348930032486263904766913919653624379586487437915175106407348e-11,
    3.692108808871129411604189196259677640440919369478263728899602e-11,
    -3.327008967125979929910636246337150851642079794871116041187279e-12,
    -1.324334917243963163878274345609465717294426628053460151843705e-12,
    4.445467096291932163298411852093011459626037560439178917611592e-13,
    -5.559442050579014337641375730083534521513818164827556763756543e-14,
    2.699382879762665647295493928801387173921314576598505507855504e-15 };

const double db32[64] = {
    1.161463302135014885567464100760659332951431420121048996305591e-05,
    2.466566906380903352739104211274667134470169443886449124673996e-04,
    2.431261919572266100780423071905958127811969678055971488060574e-03,
    1.468104638141913563547809006402194831107662001343421893488086e-02,
    6.025749912033537081745451975527967031851677384078997261920024e-02,
    1.757507836394388988189299915753348505208376399651864661397588e-01,
    3.675096285973496361995340339143234125206079560406868595968025e-01,
    5.343179193409538322901117858552186425529774700290587495921679e-01,
    4.778091637339484033555130814414794130354053753675509287934741e-01,
    1.206305382656178269538098710665261299391507308342013788891222e-01,
    -2.666981814766755535489784087869865024226542605534080371507405e-01,
    -2.774215815584272153338153320303401666681294506143291967655666e-01,
    6.471335480551623831000090095167664918448659157720155321560811e-02,
    2.483106423568801736064852157222867588791898170114101300999760e-01,
    2.466244483969740441701479334808723214802614938081258920635302e-02,
    -1.921023447085468984341365278247990525863123891147783426068990e-01,
    -4.899511718467173853355943225576377418394280156945986899417475e-02,
    1.452320794752866460838830744051944832326998342053148426312341e-01,
    4.440490819993974022640619534046603571086531544468421519143629e-02,
    -1.094561131160893831027722774343269232755171130623890041619420e-01,
    -2.962787250844770491204452379051215505049068645551070779367843e-02,
    8.087414063848395744090831590426327690818854671836423275412813e-02,
    1.410615151610660772869738802931740150275269382463799031013905e-02,
    -5.692631406247843550478416271158537960555270097953330567652364e-02,
    -2.380264464932573834443178362086503847328134994591954135879789e-03,
    3.705145792354468010437633458013030898015496905609424004450953e-02,
    -4.145907660827218781460700428862611061267328108653649653634276e-03,
    -2.166282283639119347634778516947485598599029367518033869601702e-02,
    6.167527310685675112579059689520105004744367282412921739811164e-03,
    1.101740071540688116532806119564345712473051769079712407908648e-02,
    -5.411568257275791208581502410752383050600045942275647685361370e-03,
    -4.649216751184411528658094984504900172989190128905887602541396e-03,
    3.627224640687864960122122984391704782343548385375321260251988e-03,
    1.468955100468467772528811782840480639166582822577191079260543e-03,
    -1.964740555821778254183647540656746450092725858126595984907304e-03,
    -2.211678729579097916278097586914956834196749138610403102772710e-04,
    8.673058518450555343925662389563539890596549655683386287799624e-04,
    -1.024537310607396186949656796812972062290796122915930356634122e-04,
    -3.059654423826911750479261161552574500739091332121504634422577e-04,
    1.053915461739828114700905192091104141076083602686374410146603e-04,
    8.103678329134838389828091896334156224227821362491626044950428e-05,
    -5.259809282684322782648914338377962890245975842272425408122506e-05,
    -1.294045779405512723950480259110995722517019870286295908085366e-05,
    1.824268401980691220603850117995712615809177092802967489081228e-05,
    -6.361781532260254953363913076575914206506177493714496098327288e-07,
    -4.558309576264423135123964145585288808181431652781253437738445e-06,
    1.202889036321620990296134494079846952404216422923750605507047e-06,
    7.560047625595947819392627283726711361273296630256477108501994e-07,
    -4.285970693151457255418342315045357407199066350632593899896712e-07,
    -5.003361868748230293692887222336390314786090450819216035110269e-08,
    8.965966311957728376981484572655177545054433542721057470726361e-08,
    -1.219924359483373093110396748985081720383992859961285213840740e-08,
    -1.104383021722648979552131128575075255513372249283096583736746e-08,
    4.250422311980592983740943309197245384991941251563471671065543e-09,
    4.384387799940474369553236949848427579687147486892033587998023e-10,
    -5.881091462634605628881794361152305108432139465417759716875076e-10,
    8.904723796221605490455387579189371137903330749397374037644960e-11,
    3.263270741332907875981844980104948375955551273115386408552080e-11,
    -1.430918765169202320188022211739750594608742928641485026836608e-11,
    1.075610653501062115165734990153347111902874668945095034791947e-12,
    5.361482229611801638107331379599434078296259332654994508124989e-13,
    -1.663800489433402369889818192962259823988673359967722467427927e-13,
    2.000715303810524954375796020597627467104635766752154321244151e-14,
    -9.421019139535078421314655362291088223782497046057523323473331e-16 };

const double db33[66] = {
    8.186358314175091939858945975190102731733968885547217619434602e-06,
    1.791016153702791479424389068736094134247294413108336017758506e-04,
    1.822709435164084208084617771787691709255513374281497713580568e-03,
    1.139594337458160925830840619716397130445853638888472948832932e-02,
    4.861466653171619508385707681587366397164931431125053574327899e-02,
    1.481863131800528081784673514426737436792606299953305691300616e-01,
    3.267181301177075783930752787756046348844272437670999719562429e-01,
    5.093761725149396552227892926384090200953139820961482931291482e-01,
    5.112547705832674655425831875568453973369927971748064975152374e-01,
    2.095823507130554216526494469993023406452629154801126958766008e-01,
    -2.042026223985421049629055102642279430174095014493415546881477e-01,
    -3.159974107665602561905181464284910961862968513875028980451424e-01,
    -1.927833943695275915600583425408664108893845271616240406358226e-02,
    2.454206121192791114179964351253140999836791489738418857473689e-01,
    9.985155868033815698139640215477639365289384281516885362929979e-02,
    -1.714280990518593279308738113273443832545615219650436927029674e-01,
    -1.108441331167107910806084983056783194189909198734302929909672e-01,
    1.219678564037346149389134584371009777591763921148126952722200e-01,
    9.478808805061595889263191779090571160237408179346345390888721e-02,
    -9.114696835133148913093153757138373418923462847746880902676089e-02,
    -7.030248505405615921453280814171665167171986608963193275084895e-02,
    7.019114394099653254998935842432841393915841096633514680190145e-02,
    4.573456189389667743139040427641638967843459421665709740086516e-02,
    -5.347125133582228919431110824663168583260050383336359554980188e-02,
    -2.524858297747649929258392207837724793937727346177294684700378e-02,
    3.868706076024496481748675031852528047303323816250150793091832e-02,
    1.070326582001954942654534968137727769698168853186071888736311e-02,
    -2.572876175473297336123211392278301875687760837710204579628265e-02,
    -2.167758617353607324783298657172830203896433848418061622436727e-03,
    1.531695411585766548347442266431874060229304787191589430967538e-02,
    -1.594288782414604768637856446111392724059836934455189837500244e-03,
    -7.953540387057939240459305406538116220678495240302592677582773e-03,
    2.389062408165908575935815973439728988151836094753689966108405e-03,
    3.480800953405711999411461002429227385937942254778524257436278e-03,
    -1.860718214455795912074482150710567824317228203897000129729967e-03,
    -1.204309257604658876916644980097327372892008586047095719636829e-03,
    1.074380696351291355073899234941719080473877020595209197706651e-03,
    2.727305847336937211749282358350196461733595290569540045817329e-04,
    -4.908329007590351474487792254066540683724948757382104652497458e-04,
    4.393166251766185755059005296958129844094063524324718175254673e-06,
    1.780431898251245351831728023200069586928513661382622116969992e-04,
    -4.160438516273709306234368807933932360567787692918883118883736e-05,
    -4.929564423417301834310231482621574127409950921583062559483686e-05,
    2.423335398816890365621188379922041046073808819182024026589770e-05,
    9.070805757828453800203677464921508178468256685438211818575040e-06,
    -8.866121366757736169176034432364298134186929098274651022820760e-06,
    -3.607516102879771631230351118595069330196155459105589342866625e-07,
    2.288371276141527305481395545993763010565968667577768164201792e-06,
    -4.426923407952870147984002129341809185622768353983550670755106e-07,
    -3.985791291985944076942626511739220753169387460984290019185514e-07,
    1.822443332571053437467128998002798233969112236553215291639303e-07,
    3.377972703730854377516206663481869099376154259897212784144779e-08,
    -3.987838198518880722819502850814936369197384392561970319349663e-08,
    3.672863576838181340505563759379169099717712645283448779390320e-09,
    5.111211857347453839549366593998758891130921028374576213256027e-09,
    -1.671392677251932495173219614104411841891545601521784559793012e-09,
    -2.496402105246193648073519269370197331176405371538404298745013e-10,
    2.426833102305682309891302883361232297664099485514601790344279e-10,
    -3.049574453945863430361296931455141500128170151643206937547928e-11,
    -1.420236859889936792437077844940412749343225644487770840543290e-11,
    5.509414720765524548752673631197714447818740985929081064907524e-12,
    -3.343481218953278765982532722689984725170758193566174566492199e-13,
    -2.152488386833302618520603545685994753329478275805993737095214e-13,
    6.214740247174398315576214699577230693021307854673557214652751e-14,
    -7.196510545363322414033654470779070592316600780697558361083151e-15,
    3.289373678416306368625564108782095644036415401902518812978798e-16 };

const double db34[68] = {
    5.770510632730285627466067796809329117324708919047900817738025e-06,
    1.299476200679530037833484815390569400369432658207722720405084e-04,
    1.364061390059049998200014449396877439591680435610837369411339e-03,
    8.819889403884978803182764563095879335330977939541630862804757e-03,
    3.904884135178594138905026219591569204043816577941517019631916e-02,
    1.241524821113768081954449898210969172708199672428635378051285e-01,
    2.877650592337145629334256618087718872558560120999651277991839e-01,
    4.784787462793710621468610706120519466268010329031345843336104e-01,
    5.305550996564631773133260223990794445605699030503652382795600e-01,
    2.903663295072749510455945186199530115755664977934564128822650e-01,
    -1.282468421744371672912377747048558427612774932943748628650824e-01,
    -3.315253015083869417715548463087537345035828886426345397256876e-01,
    -1.038919155156404718287260506925867970596448618647006698388596e-01,
    2.169072201874275950610018667099322465619408030256534197819784e-01,
    1.666017504122074437311574334509261366682993700573488534577890e-01,
    -1.273373582238011562843862636988693890108793629966541695807247e-01,
    -1.609249271778668063014799490429649196614628857267382976958607e-01,
    7.799184693794810738265349531832015087096882277333968473726399e-02,
    1.341259602711361284802399913977387999358280900708582462625539e-01,
    -5.448296806413904636632671383140642554265865948686157271017286e-02,
    -1.029475969928140852342073823689090498245496056845473569066667e-01,
    4.357609464963129726428486610925800727137724136370669421246609e-02,
    7.318523543679560555546221335452045680757998947493883124934567e-02,
    -3.701283841786244960356402125554190040750079009127461655784927e-02,
    -4.743855964527776247220681410983851377889756018716427358008296e-02,
    3.073974657395934459931226513844134346305562928466993208164603e-02,
    2.722835075635419610095839895805858855202745897718117731496534e-02,
    -2.367173792282636485046786438094940427456079528043555566867110e-02,
    -1.314398001665716086105827506126287041342680578404007359439612e-02,
    1.640937419986519252112261495537409592363156309874473310057471e-02,
    4.713649260999809905918876125437488856235874027077755004539205e-03,
    -1.004550670836151917439146861146431000364858401181337134891421e-02,
    -6.194748845153872839014356621835501857322345445234809347431098e-04,
    5.334950768759936032170270195983921511565539100791906952901398e-03,
    -7.692127975067836975989490900561029844887285335804349474993607e-04,
    -2.399453943537055863933124827688081952701780599883067560501870e-03,
    8.589959874363661955444898475746536583497522107459291718900058e-04,
    8.751999064078688732610570055224339733760304773327228476255647e-04,
    -5.527355762144197975516415296735124460550632283763688359649888e-04,
    -2.326732140233531635428863212833942245597361085708567528230733e-04,
    2.650772397558057819755811309071002543822145660933016957735937e-04,
    2.660050018453441903046828468025589086403126180798464347801678e-05,
    -9.914697770780134603580350758869378471802751837608461971022567e-05,
    1.353117227249649581251887376414486225127346352042209141315562e-05,
    2.844951419697807376503080001943765930601242225183893658540032e-05,
    -1.057657494257950623848316304755218120233253479317574337409622e-05,
    -5.710826510998303938275050074333400305512451419983646591762318e-06,
    4.169871758547028398316761659984928804362023643629741358799744e-06,
    4.979718101421307748081857636471761057429219265531618602960147e-07,
    -1.116306534817008428597995070751765080383261658112656948526954e-06,
    1.448195708333185127061180618150009526758658641231104901703561e-07,
    2.025990666667859216690536885693725545344933235432307649205497e-07,
    -7.526701740412589411177481797841044281662555785969415398369019e-08,
    -1.990346501531736915866180448337614967570744211158241514589121e-08,
    1.740423332936068076497051274445147160190783847854409836489662e-08,
    -8.665744261368722215864741166245385888818567571145958531936939e-10,
    -2.316501946995482751582294240136010067415084499025753117941001e-09,
    6.446378210323402313101214894500231181606520211579581132442548e-10,
    1.300410318609415248880403259300467720631189120978928377152233e-10,
    -9.904774537632409015479530333979124540183199174591377762845227e-11,
    1.004208735461769864836516428998306778031143650101842361622330e-11,
    6.080125354000167254059025929915591291115751734288584563131636e-12,
    -2.107879108915301546285370395443778864676275235126044599683271e-12,
    9.799451158211597727901178520526388692140586041163624252991805e-14,
    8.579194051799733179793112298652600511486581216528683482143106e-14,
    -2.317083703906408481078257081903089523234020423092175261925515e-14,
    2.587338381935699555813538163144986688834142571207152879144731e-15,
    -1.148944754480590128244815794312606245147888158018823490936280e-16 };

const double db35[70] = {
    4.067934061148559026665247110206084571051201477121972612218005e-06,
    9.421469475576740631603027533116630224451049736050903361458759e-05,
    1.019122680375098109319314672751485080202557607467199213778085e-03,
    6.807292884319132011971333979015625113494050642797397817625326e-03,
    3.123628851149071453063391210769353068187088999495893257051179e-02,
    1.034044558614783789938787754929279183985553322796063517049140e-01,
    2.513073789944933128513251971488905042866779761014740192816902e-01,
    4.435927392240354378183910489448494594782039032807956294826105e-01,
    5.370084275091661028670690231716974547580034932361053607723887e-01,
    3.603456405180473278744458573988718422538114217890792270621563e-01,
    -4.388388187393404111343479394097224312100349011932028865098625e-02,
    -3.238228649121161212147302807993176715625480327235512530593160e-01,
    -1.817869767667278325788350264528191676841493369460849123538616e-01,
    1.660413574907809195438433327470947940538097914525298064477785e-01,
    2.172992893210892977675493456199559114036326358517672106972956e-01,
    -6.526287131067753892154895911331108284007380738865652420304233e-02,
    -1.919195892985939528760786800798636198516495957924798820500876e-01,
    1.930954466601835091947734585938109944647435243484967057775110e-02,
    1.552924803962371144206753760712566993987319378965231186477630e-01,
    -4.752680834111350445288110998030979143710864689041902167119118e-03,
    -1.205855226433935545076589480704957722635324456812322150437989e-01,
    4.734229172641948763293980314992213293971770695480616789828384e-03,
    8.991354757072954417865374195261962983644048998218233900481856e-02,
    -9.318558949903924837875002823617504227246562152671894579504378e-03,
    -6.335603744044346612098887534020545705731671718057964802006671e-02,
    1.322854958503655524455929847605110719648746890497356808289302e-02,
    4.125469306470509212749750814299126656151504805845417994651417e-02,
    -1.436683978422007182104025173214012797788904894291716373493525e-02,
    -2.416949780166026740294880681731084091264533168816746227537030e-02,
    1.276645671565674419403918018742432714973656598227939824940035e-02,
    1.228943600811871086161967625814297050611100200023898377949151e-02,
    -9.577797899235709998147309703713518608283233882793489733491642e-03,
    -5.085991649233429881797636583578921194675393807761154549733547e-03,
    6.137754586740521089596801883631921221145712545042519987641234e-03,
    1.428088794070762107355585870669842132609159040625895090070111e-03,
    -3.357644380922383229567732565298665639037348585961127075507937e-03,
    7.615969435172736546769649923895317451534703066016116257300160e-06,
    1.549637469702362975561719246539787717204438637997824935787688e-03,
    -3.346692164250854961608526121524596908041109918361306282201310e-04,
    -5.864810318991817532175809224131456738367101035694188223408841e-04,
    2.648328819961289039302810122699710966048565368047575218693134e-04,
    1.700012283661249043584690194716767771204207742625746308522935e-04,
    -1.365883072261161602559926714744746422567509177443594045709653e-04,
    -2.976995962848509743944225866488519668585242655980656646544319e-05,
    5.304143122913310222538317980686374696005605533475685587486683e-05,
    -2.437001526827789860990429478540556752694389693432668831073769e-06,
    -1.572442077270281693663288966405861215692805972737981986121447e-05,
    4.308047861716731191350493437937513220737450410132878032163179e-06,
    3.353345862871309889390877168046133657377105681618708355266688e-06,
    -1.895929617693153288493891051875444439753318548105998166574535e-06,
    -3.903931733287306166657519468494511920760767388397825775326745e-07,
    5.302368616904760917074352633915743250769600635829229600812520e-07,
    -3.700308378205124537986402644918879149894035910106489082512364e-08,
    -9.990396944534900755781728477561240762191443422318249128866740e-08,
    3.008188650719066928230268918661718274504955045022550217051301e-08,
    1.084902733789934825266560240100449884702749303326571747323086e-08,
    -7.458116552893037631192407611262788593505988638365840409367117e-09,
    5.897951310384361575470355861162022501172491937837712969865619e-11,
    1.030823345485433383811700481488557422005210168069163779730908e-09,
    -2.433545573751672936168877250405940817227367937230289801251648e-10,
    -6.407938256501889018430608323235974406219193176918284664973727e-11,
    4.000536627253744510742788201354093006471710416671002244302586e-11,
    -3.125639357108557540598098228678150768528121565391376265627294e-12,
    -2.567065476155081449204643852428401530283519685638256074752850e-12,
    8.015088533687900921948605418789324826115616416343391081288979e-13,
    -2.597954328893848084315198205094389145706680129208998638802995e-14,
    -3.397720856796267431956783825659069596940335130100871912329556e-14,
    8.624037434720089202680337663692777682810714650060805832406135e-15,
    -9.298012529324185420921555664719863501848315099116725184370339e-16,
    4.014628712333488654318569164614220308046021091178184654250982e-17 };

const double db36[72] = {
    2.867925182755946334630479473029238615535511775894262711054705e-06,
    6.826028678546358691748629102209605362240344266505035981791715e-05,
    7.602151099668488285869792677106082100141275054892389379198545e-04,
    5.240297377409884366201603524392995696042174937194435235003941e-03,
    2.489056564482796484885927333959115579403023347044729739255255e-02,
    8.565209259526409083864716995521111486437594750377856524772704e-02,
    2.177569530979008149637945915719999746248969705650625533415876e-01,
    4.064336977082553467407793990250384445903151630768558142125382e-01,
    5.322668952607286914777444748641462027213554723153906901129337e-01,
    4.178753356009697863620634559374236455222275302996931178265919e-01,
    4.397519752934862993862182898358763783110745559238982179690132e-02,
    -2.944210395891145711100715969898758940722458887377844633443675e-01,
    -2.468070369781255270524798278622698446566520718230313889086016e-01,
    9.811420416311477050518401371401568038943437322299913514049728e-02,
    2.465372776089742110529709111809595434656418762898152706621356e-01,
    7.278515095792229009687682299460382878643139026668958884429641e-03,
    -1.993372056086496198603363400094784142714162256792182570541036e-01,
    -4.586140074639271639145126228774831743002971373998329604574394e-02,
    1.541062366276428841776316300420654875883842819413623395358262e-01,
    5.027618007353842862036816972809884096761706036019748316890913e-02,
    -1.188037543101356316801816931383547446073152951044444224449501e-01,
    -3.988085357551317584091699967924044034100374257075864260934102e-02,
    9.115678225801654406336059281306715151058903055370522031843771e-02,
    2.503872144956848989919484296709846860569180993040383621980546e-02,
    -6.820901663681751124880436344265538690580358108714540763125119e-02,
    -1.131910031681742794381808082173695022123056280821611354577883e-02,
    4.851308354780908538616267662315735632292989749013261207046367e-02,
    1.424972661765391603147802607378542396323429657660009755652404e-03,
    -3.198072067763969654470293513742344601172739688274251641873778e-02,
    3.984040198717004857397179486790082321314291366656151213429068e-03,
    1.906359478062535932877576164368198274858108513696832728889209e-02,
    -5.657813245058818380424016973516714570499161434975761798379020e-03,
    -9.990263473281372348001743806489172665465685056975652497503772e-03,
    5.022989106665829004699819220796538830393945994687289792465541e-03,
    4.413484835350575251918616780287775585471012556848037301025999e-03,
    -3.484541445404883311209541395428535732697661971818727286003028e-03,
    -1.503074066296643749549363655363411879858070202740814054964603e-03,
    1.990793771851737270404293245701878186600899439513475823305914e-03,
    2.776812795712026068152384207605140383490242756921936501940389e-04,
    -9.463403823261101964604918059447913047725482130063492242779878e-04,
    8.614565758992702032613879159402330909634737204578606399403107e-05,
    3.693507284967510502620040341882236687749563414433432842567511e-04,
    -1.155118895843527096848376999413102395191976350936666573818799e-04,
    -1.131899468084665671727391922924411467938450743565106978099456e-04,
    6.694741196930590257104231749283786251555566773398199990337698e-05,
    2.375106683660860777161950832380341362257503761490580896617678e-05,
    -2.731390824654337912922346414722045404779935825834384250023192e-05,
    -1.183471059985615942783182762352360917304348034947412986608322e-06,
    8.372218198160788432628056043217491552198857358432112275253310e-06,
    -1.586145782434577495502614631566211839722879492827911790709498e-06,
    -1.870811602859180713762972281154953528056257451900381097476968e-06,
    8.311421279707778528163597405935375886855029592150424544500718e-07,
    2.548423522556577831218519052844387478819866531902854523544709e-07,
    -2.455377658434232699135878286794578515387138194247693201846263e-07,
    2.753249073339512254085076456700241929492720457889076058451072e-09,
    4.799043465450992009934526867650497683545716858606119786327559e-08,
    -1.156093688817008406756913949175208452083765368825442482226093e-08,
    -5.612784343327791397474114357094368557982413895802980814813369e-09,
    3.138841695782424018351567952158415003571380699236147752239001e-09,
    1.090815553713751810964713058800448676068475673611349566405716e-10,
    -4.512545778563249634425200856088490195004077806062978067796020e-10,
    8.962418203859611987065968320295929679774693465791367610044773e-11,
    3.037429098112535221800013609576297196061786927734556635696416e-11,
    -1.599716689261357143200396922409448515398648489795044468046420e-11,
    8.876846287217374213524399682895564055949886050748321818411161e-13,
    1.070969357114017002424433471621197579059927261727846375968378e-12,
    -3.029285026974877268896134589769473854669758797446795757329862e-13,
    5.542263182639804235231685861028995158694397223907295269180336e-15,
    1.338071386299105896025578761458472955294763310766371178363783e-14,
    -3.204628543401749860439316638848579711789176444320134355253750e-15,
    3.339971984818693213132578777712503670014459411167839211495237e-16,
    -1.403274175373190617489823209168013922564353495443487431242610e-17 };

const double db37[74] = {
    2.022060862498392121815038335333633351464174415618614893795880e-06,
    4.942343750628132004714286117434454499485737947791397867195910e-05,
    5.662418377066724013768394373249439163518654840493603575144737e-04,
    4.024140368257286770702140124893772447952256842478891548092703e-03,
    1.976228615387959153244055502205017461538589475705618414896893e-02,
    7.058482597718160832030361890793007659963483925312132741868671e-02,
    1.873263318620649448028843491747601576761901656888288838192023e-01,
    3.684409724003061409445838616964941132670287724754729425204047e-01,
    5.181670408556228873104519667534437205387109579265718071174178e-01,
    4.622075536616057145505448401528172070050768534504278694229363e-01,
    1.308789632330201726057701201017649601034381070893275586898075e-01,
    -2.461804297610834132869018581145720710365433914584680691693717e-01,
    -2.943759152626617722808219575932673733674290772235644691367427e-01,
    1.967150045235938977077768648740052380288156507222647187301894e-02,
    2.515232543602686933435224095078166291442923992611593827552710e-01,
    8.180602838721862339029076982652411696000045533716726027662147e-02,
    -1.819622917786080007408824256525225216444443143868752611284260e-01,
    -1.084517138233017845554078812341876568514835176341639783558543e-01,
    1.299296469598537527842528895259188653120602318620944502979726e-01,
    1.017802968388141797470948228505865617480048287983176581607964e-01,
    -9.660754061668439030915405045955772715988585374771282291315496e-02,
    -8.233021190655740867404073660920379414988302492018783774702028e-02,
    7.504761994836017933579005072594245435071674452882148228583865e-02,
    5.956741087152995245435589042520108066877114768216272503684398e-02,
    -5.925681563265897095153806724965924334077555174281436189512239e-02,
    -3.825382947938424882011108885090442116802994193611884738133373e-02,
    4.580794415126833246633256156110381805848138158784734496981778e-02,
    2.097280059259754883313769469036393294461497749083921162354229e-02,
    -3.352358406410096994358662875913243067234786296009238949920582e-02,
    -8.833493890410232394064187990625563257107429109130726291528648e-03,
    2.261865154459947356571431658958802912061105608212828675323452e-02,
    1.690472383484423743663952859090705636512807161536954018400081e-03,
    -1.376398196289478433857985486097070339786225136728067000591187e-02,
    1.519305778833399218481261844599507408563295102235964076544334e-03,
    7.387757452855583640107787619408806919082115520707105052944171e-03,
    -2.248053187003824706127276829147166466869908326245810952521710e-03,
    -3.394523276408398601988475786247462646314228994098320665709345e-03,
    1.816871343801423525477184531347879515909226877688306010517914e-03,
    1.263934258117477182626760951047019242187910977671449470318766e-03,
    -1.111484865318630197259018233162929628309920117691177260742614e-03,
    -3.280788470880198419407186455190899535706232295554613820907245e-04,
    5.490532773373631230219769273898345809368332716288071475378651e-04,
    1.534439023195503211083338679106161291342621676983096723309776e-05,
    -2.208944032455493852493630802748509781675182699536797043565515e-04,
    4.336726125945695214852398433524024058216834313839357806404424e-05,
    7.055138782065465075838703109997365141906130284669094131032488e-05,
    -3.098662927619930052417611453170793938796310141219293329658062e-05,
    -1.639162496160583099236044020495877311072716199713679670940295e-05,
    1.354327718416781810683349121150634031343717637827354228989989e-05,
    1.849945003115590390789683032647334516600314304175482456338006e-06,
    -4.309941556597092389020622638271988877959028012481278949268461e-06,
    4.854731396996411681769911684430785681028852413859386141424939e-07,
    1.002121399297177629772998172241869405763288457224082581829033e-06,
    -3.494948603445727645895194867933547164628229076947330682199174e-07,
    -1.509885388671583553484927666148474078148724554849968758642331e-07,
    1.109031232216439389999036327867142640916239658806376290861690e-07,
    5.350657515461434290618742656970344024396382191417247602674540e-09,
    -2.252193836724805775389816424695618411834716065179297102428180e-08,
    4.224485706362419268050011630338101126995607958955688879525896e-09,
    2.793974465953982659829387370821677112004867350709951380622807e-09,
    -1.297205001469435139867686007585972538983682739297235604327668e-09,
    -1.031411129096974965677950646498153071722880698222864687038596e-10,
    1.946164894082315021308714557636277980079559327508927751052218e-10,
    -3.203398244123241367987902201268363088933939831689591684670080e-11,
    -1.398415715537641487959551682557483348661602836709278513081908e-11,
    6.334955440973913249611879065201632922100533284261000819747915e-12,
    -2.096363194234800541614775742755555713279549381264881030843258e-13,
    -4.421612409872105367333572734854401373201808896976552663098518e-13,
    1.138052830921439682522395208295427884729893377395129205716662e-13,
    -4.518889607463726394454509623712773172513778367070839294449849e-16,
    -5.243025691884205832260354503748325334301994904062750850180233e-15,
    1.189012387508252879928637969242590755033933791160383262132698e-15,
    -1.199280335852879554967035114674445327319437557227036460257649e-16,
    4.906615064935203694857690087429901193139905690549533773201453e-18 };

const double db38[76] = {
    1.425776641674131672055420247567865803211784397464191115245081e-06,
    3.576251994264023012742569014888876217958307227940126418281357e-05,
    4.211702664727116432247014444906469155300573201130549739553848e-04,
    3.083088119253751774288740090262741910177322520624582862578292e-03,
    1.563724934757215617277490102724080070486270026632620664785632e-02,
    5.788994361285925649727664279317241952513246287766481213301801e-02,
    1.600719935641106973482800861166599685169395465055048951307626e-01,
    3.307757814110146511493637534404611754800768677041577030757306e-01,
    4.965911753117180976599171147718708939352414838951726087564419e-01,
    4.933560785171007975728485346997317064969513623594359091115804e-01,
    2.130505713555785138286743353458562451255624665951160445122307e-01,
    -1.828676677083358907975548507946239135218223185041410632924815e-01,
    -3.216756378089978628483471725406916361929841940528189059002548e-01,
    -6.226650604782432226643360160478765847565862101045597180310490e-02,
    2.321259638353531085028708104285994998671615563662858079262996e-01,
    1.499851196187170199586403453788927307298226028262603028635758e-01,
    -1.417956859730596216710053144522330276392591055375830654519080e-01,
    -1.599125651582443618288533214523534937804208844386102639177693e-01,
    8.563812155615105741612217814369165313487129645536001850276987e-02,
    1.414147340733826800884683119379170594092606174915755283496153e-01,
    -5.658645863072738145681787657843320646815509410635114234947902e-02,
    -1.147311707107443752394144019458942779715665489230169950201022e-01,
    4.309589543304764288137871223616030624246568683595408792078602e-02,
    8.720439826203975011910714164154456762073786124233088471855868e-02,
    -3.660510340287429567372071039506772372567938710943432838908247e-02,
    -6.176620870841315993604736705613246241897497782373337911398117e-02,
    3.198987753153780630818381136366859026137035450576631134176875e-02,
    4.005498110511594820952087086241114309038577379366732959648548e-02,
    -2.689149388089451438550851767715967313417890393287236700072071e-02,
    -2.311413402054931680856913553585621248925303865540203357180768e-02,
    2.090464525565524340215982365351342094670261491526831672682244e-02,
    1.129049727868596484270081487761544232851115891449843967151657e-02,
    -1.470188206539868213708986402816605045648481224662435114088245e-02,
    -4.131306656031089274123231103326745723188134548520938157995702e-03,
    9.214785032197180512031534870181734003522861645903894504302286e-03,
    5.625715748403532005741565594881148757066703437214522101740941e-04,
    -5.071314509218348093935061417505663002006821323958752649640329e-03,
    7.169821821064019257784165364894915621888541496773370435889585e-04,
    2.400697781890973183892306914082592143984140550210130139535193e-03,
    -8.448626665537775009068937851465856973251363010924003314643612e-04,
    -9.424614077227377964015942271780098283910230639908018778588910e-04,
    5.810759750532863662020321063678196633409555706981476723988312e-04,
    2.817639250380670746018048967535608190123523180612961062603672e-04,
    -3.031020460726611993600629020329784682496477106470427787747855e-04,
    -4.555682696668420274688683005987764360677217149927938344795290e-05,
    1.262043350166170705382346537131817701361522387904917335958705e-04,
    -1.155409103833717192628479047983460953381959342642374175822863e-05,
    -4.175141648540397797296325065775711309197411926289412468280801e-05,
    1.334176149921350382547503457286060922218070031330137601427324e-05,
    1.037359184045599795632258335010065103524959844966094870217687e-05,
    -6.456730428469619160379910439617575420986972394137121953806236e-06,
    -1.550844350118602575853380148525912999401292473185534395740371e-06,
    2.149960269939665207789548199790770596890252405076394885606038e-06,
    -8.487087586072593071869805266089426629606479876982221840833098e-08,
    -5.187733738874144426008474683378542368066310000602823096009187e-07,
    1.396377545508355481227961581059961184519872502493462010264633e-07,
    8.400351046895965526933587176781279507953080669259318722910523e-08,
    -4.884757937459286762082185411608763964041010392101914854918157e-08,
    -5.424274800287298511126684174854414928447521710664476410973981e-09,
    1.034704539274858480924046490952803937328239537222908159451039e-08,
    -1.436329487795135706854539856979275911183628476521636251660849e-09,
    -1.349197753983448821850381770889786301246741304307934955997111e-09,
    5.261132557357598494535766638772624572100332209198979659077082e-10,
    6.732336490189308685740626964182623159759767536724844030164551e-11,
    -8.278256522538134727330692938158991115335384611795874767521731e-11,
    1.101692934599454551150832622160224231280195362919498540913658e-11,
    6.291537317039508581580913620859140835852886308989584198166174e-12,
    -2.484789237563642857043361214502760723611468591833262675852242e-12,
    2.626496504065252070488282876470525379851429538389481576454618e-14,
    1.808661236274530582267084846343959377085922019067808145635263e-13,
    -4.249817819571463006966616371554206572863122562744916796556474e-14,
    -4.563397162127373109101691643047923747796563449194075621854491e-16,
    2.045099676788988907802272564402310095398641092819367167252952e-15,
    -4.405307042483461342449027139838301611006835285455050155842865e-16,
    4.304596839558790016251867477122791508849697688058169053134463e-17,
    -1.716152451088744188732404281737964277713026087224248235541071e-18 };

// All coif coefficents have to be multiplied by sqrt(2)
static const double coif1[6] = {
    -5.142972847076845595317549230122688830344559947132656813651045e-02,
    2.389297284707684559531754923012268883034455994713265681365104e-01,
    6.028594569415369119063509846024537766068911989426531362730209e-01,
    2.721405430584630880936490153975462233931088010573468637269790e-01,
    -5.142972847076845595317549230122688830344559947132656813651045e-02,
    -1.107027152923154404682450769877311169655440052867343186348954e-02
};


static const double coif2[12] = {
    1.158759673871686817889714882853120395708315073355502818875931e-02,
    -2.932013798346856448679594524397843054053420947418409889774786e-02,
    -4.763959031100813225872995081511549408622753909592460525840745e-02,
    2.730210465347666137982239328923516270034828327990699588033501e-01,
    5.746823938568638472459483149751499367740786490481481391460366e-01,
    2.948671936956191896750637208703777973914107635455611537640778e-01,
    -5.408560709171142997443672832006888537570221990444706777525838e-02,
    -4.202648046077160694657530752545884878978719268926222513485613e-02,
    1.674441016327950635146257083249391698866289538037299820224006e-02,
    3.967883612962012109043447090269950094081810916481648252817197e-03,
    -1.289203356140659543141355500990678257894936161704492503370186e-03,
    -5.095053991076441489598480835620951586540050976664367876412655e-04
};

static const double coif3[18] = {
    -2.682418670922068664584689955153722375535836177157637134187840e-03,
    5.503126707831385107969640263617469178794666057252906037981936e-03,
    1.658356047917034608134280439996549525220639437145367606178002e-02,
    -4.650776447872697640390293095170192691113917841041002855534619e-02,
    -4.322076356021191118175840907244577856782537221435748296465882e-02,
    2.865033352736474630249006862976158896891076238443844211133873e-01,
    5.612852568703300445990941995240077241406247774064453800050914e-01,
    3.029835717728241602862575774374668529867757043461413348549577e-01,
    -5.077014075488886159516471867138370972545857441670871832472707e-02,
    -5.819625076158553022607041679522801089624825903982541419721721e-02,
    2.443409432116695639462954438418928805487699080947974989338820e-02,
    1.122924096203786563399489540091488781245346096838814728167341e-02,
    -6.369601011048822977293753932627342482077585617391852852955559e-03,
    -1.820458915566242322836631665832145136570132777862391313328351e-03,
    7.902051009575939937150950543290226440287715441826917281929124e-04,
    3.296651737931830308416338897758022998655744276957481989605186e-04,
    -5.019277455327664998007173088097694083956570594580641192332170e-05,
    -2.446573425530813115445387662881902303945941576472342106918209e-05
};


static const double coif4[24] = {
    6.309612114309468490753696608619526520153127603444406835368201e-04,
    -1.152225143769973488683007937016166047881572156705066038094891e-03,
    -5.194525163470323267558201363327294331811309729430512113592118e-03,
    1.136246148326482276463392678363118465908960082105224676102131e-02,
    1.886723856956305960822813160712701905823879297781452350370094e-02,
    -5.746424190192718517290527411385172124443396690932404284859269e-02,
    -3.965265296244913762718094206756579981738035770770645437919302e-02,
    2.936674050161006858761278962798582650835466243678172528509866e-01,
    5.531264550395492870333469741987846570947502710783248169642137e-01,
    3.071573096678856987248881030393884808414165269795297009902001e-01,
    -4.711273752389572084912399351781012121935994396763702238263689e-02,
    -6.803811467802056988332974920928626798429778679560269769187728e-02,
    2.781363695846951303169163645831936314699164412528991864702607e-02,
    1.773583142270308388403079552822372238681544967313003044695583e-02,
    -1.075631615508724933047071603601897536695959225169888787867102e-02,
    -4.001010844950535391911552472397083276670126595827549403173754e-03,
    2.652664913530499860820143301690017184933302935238430721089152e-03,
    8.955939276952843603555618778866181384528643960440369133096025e-04,
    -4.165001950941708741516836418852536615951250588002878691463468e-04,
    -1.838296167136253805617482342622910940008368723403836355183423e-04,
    4.408022661597206973006038672236031501663774161685451815597956e-05,
    2.208284691230832960893331999804142845136324572860276715790883e-05,
    -2.304919162676504406778986897925054839632903355820414483306851e-06,
    -1.262179179994622253884862172782890488140153502131112374520603e-06
};



static const double coif5[30] = {
    -1.499645228345950331670593167919531667975440598691604525531231e-04,
    2.535527523580334712936363872191554706055603482812691726895588e-04,
    1.540286725995222360335148244676269541414659303531250711822333e-03,
    -2.941078164035693185044038586065593320891475311414770624555173e-03,
    -7.164112349410053294382279572472252500899544810929605832362178e-03,
    1.655218330649288840540841623080651353621667424921282557975513e-02,
    1.991901719798432056056857854066125809443504706772520641876273e-02,
    -6.499837825472324963374262221660858232544804226063450042795603e-02,
    -3.680255347446873527191823500872992242220223547780834450868002e-02,
    2.980959014587191795511466861338063554509597132272839414668911e-01,
    5.475082713540367154128337935687830970431964302909253422329131e-01,
    3.097002590784203529311533316221254677074498876376965941549923e-01,
    -4.386731482823615640442730013366750193381707273908757638050452e-02,
    -7.464442013283971243472663968192859562973186442054433655531762e-02,
    2.919469277528073666095772398605275751022315529465178441510318e-02,
    2.310457227706684192610065243663928370022983285246219996141160e-02,
    -1.397129268638200558584119246355879336305763752871371182932059e-02,
    -6.476749751505861835547590642967453082384538848552165075614441e-03,
    4.781116799130657606400088024549264921093190305150784065791191e-03,
    1.719383484385504023022397097446276782318002683055773803854075e-03,
    -1.174947934413537690027670037110105795928147523549002426409332e-03,
    -4.508222400696236312231932151038336110220594834213702970043431e-04,
    2.134457975086291667348984871136041914777578046177470626552867e-04,
    9.924691139873533169989496559631669037970741600337089424730635e-05,
    -2.914684388622130824599478843558087403539428940986384077972155e-05,
    -1.504031798197685905639227292876711236513927746903476131955063e-05,
    2.616809660013118152124234488302931243021794024318439103773996e-06,
    1.457502921355163070577152619048168436286350537937563166257584e-06,
    -1.148199649902979726237655584441763456854312591680755421569962e-07,
    -6.791060677322355511541065559242475254516249773485524025251102e-08
};

static const double coif6[36] = {
    3.590369176713147297619137585162985487882983106558813483919654e-05,
    -5.739587600308579302553116112640931312834124316491419262693257e-05,
    -4.416681189774217015420887950711066328841194295133755225305844e-04,
    7.718952365964601416123598337617069164684563206275891099236221e-04,
    2.502464949920583984721024494943552146200854870661622211002418e-03,
    -4.970540926794084916072709193212250530229884674851059194676904e-03,
    -8.649031599946572670088689962363342931064457587276457635862837e-03,
    2.096272704497140779792663941456374769474253019820508756967019e-02,
    2.035485665853858604541136567848434116598037957308057353920592e-02,
    -7.047945564795552885905434147081164203757922879137146339823510e-02,
    -3.448140611368923447131875491841937617457159299006945091335206e-02,
    3.010998878403483951866118786441379902683440623823004392113350e-01,
    5.433431541205752976643226361323847389491208513427704619816300e-01,
    3.114106686884303879320332890564758567306967030720816157048784e-01,
    -4.108920994584877203854246950993119698209208008049962179892675e-02,
    -7.938037857340304740867638342147592451705918503253249849143539e-02,
    2.959417996664270964779250587338231669479158250990737014414477e-02,
    2.749324945396978289421721884832761949259069726774638450310483e-02,
    -1.622820901345195945858655272139343256940218447777075044002063e-02,
    -8.944948800734973559175903045029991278019908264185511730556261e-03,
    6.781924902353432370456282863377674743820538634665380239602198e-03,
    2.727776322637165251200756406544564359327985321401403409245472e-03,
    -2.173603470504406274543289770274934991733554729244606459300157e-03,
    -8.184301467772110627838702680469158860544118224504527036324346e-04,
    5.443695006423825218320707989235387077885356717678678221237514e-04,
    2.299669354496271337796680394777537358902688275513954507765924e-04,
    -1.093025858275705388257100771765109469841731265349469854748449e-04,
    -5.323102894235956524250869703703947941287269765076967124254140e-05,
    1.749138884456354646795465632327170008020712055425505484223307e-05,
    9.291277996211799255766657547295162218792966588358292566578482e-06,
    -2.067852860108562040355056354764683583070734740977224663511355e-06,
    -1.173528057754959701015486666538811237336231287996985858558749e-06,
    1.595231380068613024221709499993144713297922814623646799204434e-07,
    9.548236102970597211555688470325396069418862428946106509526618e-08,
    -6.001316648399794543233636397364817876191104394511939069108879e-09,
    -3.754092421718877429714314525761519021787631896344301745834088e-09
};

static const double coif7[42] = {
    -8.642436297638419945610948131417508321449359042880382837616795e-06,
    1.323092600668737320653618189565188176193563004626002735569108e-05,
    1.238159302411191700694316904823586790698547751438666389964909e-04,
    -2.030827471878621660379666662315021490193874834607822591973651e-04,
    -8.268301617880235510290843066205662542616488856061409195612586e-04,
    1.489005690114437686460337716452079526238674405271430102926760e-03,
    3.414934412450460679215304700817226231104673750197184931650456e-03,
    -7.027860242269773782347401141338545341343547479199220829531083e-03,
    -9.759879698174635975799287457121964575077621800626382384905922e-03,
    2.468545489421116360102434627504896094772630565645374342185378e-02,
    2.046157861402903980087467083352777920759194756736646791948754e-02,
    -7.463948234553393791576174267216063661913646014250615692345955e-02,
    -3.255052720695225444262327386510843067531614239181091226317606e-02,
    3.032701839810768064176188260727440638632962820833222425246103e-01,
    5.400990244606528900971955967395441757408783874674606654776292e-01,
    3.126383971738483006163784309975347847899010543829381537178238e-01,
    -3.871497424779051301839419764560067767205245057921330411662831e-02,
    -8.293907947422769053048964127950429060647696494556080282464817e-02,
    2.949014117259198370970425259971349135137714379653805589390139e-02,
    3.110778126696628039496809790347026077516532785378620954115443e-02,
    -1.778674610242698080195492177636195380628008680534327644027865e-02,
    -1.127612352465961805417517163375322309389029593356595515710364e-02,
    8.522290099960163136478259451975210336102817817562221392657719e-03,
    3.840520687530513060929693850444023135936063807811968697808645e-03,
    -3.265307484878191553686608663193259573061021023330761877609170e-03,
    -1.273879229605184944648413513123685328329522201334094650099164e-03,
    1.014515696091098223010746848382433126910091720031207326903328e-03,
    4.097679895401914370060120309164154094898913488243766819496226e-04,
    -2.609696573094586622328219462237882874111293043254082981395787e-04,
    -1.186646887145157446962850804940522053646675640116219334763007e-04,
    5.636383526557468584243108976415315613118268111676116113791335e-05,
    2.858866828380331402146557751682881290895951013741692838961144e-05,
    -1.006611544197303027244467426357681984010120261232862556289963e-05,
    -5.495099010556417822017774697665452170235386110376505191968507e-06,
    1.415682965517504934011008339473818198664994789606034408294717e-06,
    8.188133233610314217903522752009348948778608588070621911086501e-07,
    -1.463230575247395655425908281663831406004216665367756779712751e-07,
    -8.874835827294529368648499409085591173735229589713890206258021e-08,
    9.853606453775557692052504971290731996468795418172901924493247e-09,
    6.220130833773100584817683046405291459615130548784852993590582e-09,
    -3.237371065873786031415974713833345092654235228652968924822315e-10,
    -2.114649662048637955296836248309680136665362492693075345297952e-10
};

static const double coif8[48] = {
    2.089031388199542217649735304633077946477575993553137874997499e-06,
    -3.088829676466960594403785936495123295795735325141186563064826e-06,
    -3.415065199420814308581377665735877442810613593316182887801764e-05,
    5.336794978475752885866749586223668039582360880077534324830961e-05,
    2.625452092146307108027768728326461036005400582791844219135485e-04,
    -4.409238208772274245312366838988697089610792049225817119782188e-04,
    -1.260955244697859087126122671343741939804055800993471565939717e-03,
    2.334035748516809401796699851512493931521487402803856088184519e-03,
    4.238998515964122916906367014472885263525332429616686168386780e-03,
    -9.010216682788485374574179959374952745626145790175446525921665e-03,
    -1.059137210972160452062877264732854070957273640727973500291258e-02,
    2.784023497265315551670806338623371354680279388669611745037381e-02,
    2.038491393825460625255433218588522954822776999829791972608492e-02,
    -7.790193780817760648856650048481215968473342802126754335290911e-02,
    -3.091398960464199635355286295557605183007519885588326376800431e-02,
    3.049113846932028296232414935045248095543039057351616871735909e-01,
    5.374812703269839615372906413514102630694929400948606989937313e-01,
    3.135612340595966380418077448086007870950549040456326333016594e-01,
    -3.667108316665038246204915903550010028035229188747930760164446e-02,
    -8.570923901203457457230067054960180625904745972345544735005669e-02,
    2.912276318941918093502808667205448538832711003842651923199100e-02,
    3.411957880301561499047582538207723820508873324357307771206279e-02,
    -1.884914078883376071043287013172570992844993887415459298291641e-02,
    -1.342459526650064517082192966318056581890805569912583356614454e-02,
    9.982558825082415904373108133470460186315362848702191453854950e-03,
    4.996294194193990739772329108378853011245087219628720892888460e-03,
    -4.353415716030435524873558659254500246467092155734985247834619e-03,
    -1.798882274878169625580021134613003566863070625422298385086326e-03,
    1.580842866685999532270584538283553531040029730619088591607990e-03,
    6.341164354094166487421798386547881496331208497274921165613427e-04,
    -4.859037288504776883185923185674204816335250660367189593401172e-04,
    -2.105615022500261821665621601578029342187313061309595147668812e-04,
    1.284762650553455535249206074613997094898698286922036784576671e-04,
    6.190332439714985153834932259246488292329875513962818749340013e-05,
    -2.932710247776567240870160300128192249260665410455632214571613e-05,
    -1.541634258578732960762233596163970199686495285352470015267722e-05,
    5.679130231196248852811714735791066544261806333156564637706958e-06,
    3.179814253819903951090335509892218398493686680727385157906016e-06,
    -9.018823843695175549580166675906964282301328192772638509290562e-07,
    -5.313922705051620291263120753790527283196870682052587236468039e-07,
    1.123841380424949688574412705744289982140965409566073018148117e-07,
    6.910143395859795983361485345968765950977315740669289349714194e-08,
    -1.028133863272738862101812736488937814136249072478179304322756e-08,
    -6.555732343557023119114074649273108865362704549509543350446188e-09,
    6.130612315575721972473587395696526436552798806104270539014282e-10,
    4.033910072490666571003953018881489938766267669840899254802129e-10,
    -1.785744077894230068079099924407862882873224386100989050429950e-11,
    -1.207731024612356156550871498168833597517735826635815624658975e-11
};

static const double coif9[54] = {
    -5.066363823597422392806056574334164019913184490360944548316318e-07,
    7.278391993125473027029583168671424388748658656372670262448419e-07,
    9.304737272738569335426840667700605845237073216830127313873309e-06,
    -1.399166643491873035927397151326087921115765448962913723958371e-05,
    -8.091702401289310224621206748421921014290936300934460833068239e-05,
    1.288948603756442891955388200793566964011433484841725529675464e-04,
    4.429833292564910878782752821721584532350914860569614883397891e-04,
    -7.604638375385009047720991576053046852032649468701121074994496e-04,
    -1.712076406330546261864476465012314688415071394908910205115863e-03,
    3.250609771558499559986242897495101339387218580957593472948506e-03,
    4.965544559205420047336110923957506351279457718709199656899118e-03,
    -1.087293322510375296786682178855708097994536728252120886318829e-02,
    -1.121487186714498120022344911296954171366809171629939577546416e-02,
    3.053409241514424830665032185876170313664500941851818086027391e-02,
    2.020392698610770380447570514810463753302939216061746963067828e-02,
    -8.052780090649459945830240285306098489931000767407230573519424e-02,
    -2.950481547909572113527928936790826747949471856175409844500779e-02,
    3.061961521402196297060170641420108731472017553153312985302650e-01,
    5.353120232520133713115706737209774227378476257207779817606799e-01,
    3.142791902169524902984239510600746503211251765067043225338625e-01,
    -3.489491800009343033063601189229669581774254383120018514658627e-02,
    -8.792560957387142697108395104950963599558244039650648988222216e-02,
    2.861927542811334614253853950282153483993260752259557922334958e-02,
    3.665967931975593019562623910949450882200844831012366323592970e-02,
    -1.955945002554213167931774103463762372167104971118382178710488e-02,
    -1.538279230927754406350075858557526842247910581873511998413085e-02,
    1.118552122310349928798438523781104471338643824158961043502858e-02,
    6.153778805250375760539535917130018322120240439861388830004320e-03,
    -5.383941047406102653501165062580070636876629329655105558566173e-03,
    -2.374234426766700376811893833905319565227030464607844605997432e-03,
    2.201384480544939153490212385667343932208692420769790727070706e-03,
    8.978070631742198872611262172632408686645841161925814232909679e-04,
    -7.748091639808001244045755147010278445955718484470643940080615e-04,
    -3.271988742130504187500457463514277973862371006416232573352337e-04,
    2.382340816470993068532708118584131071285832153866665367058914e-04,
    1.098939547757907591024137319089517624469653929668339354098746e-04,
    -6.459841534412000641464605011582059745094736001136249665670393e-05,
    -3.262384353548798509572707535297893969508001470867657290979350e-05,
    1.539823757133791368834354345267796411124232848770592546980638e-05,
    8.354049038925734685227308673812966075026848925201707946720101e-06,
    -3.173574058801678589943416130953277710277471261605337088707621e-06,
    -1.818949869332426905097083388417804018910760658657704860329107e-06,
    5.517186750972799125927280958782447793255276643806997727626076e-07,
    3.308966123618421063439933908570170555222439095610467058151640e-07,
    -7.846530733490392810972396794430929632826504340137889923489193e-08,
    -4.890737315241025544677728915264690823141541323355311455146920e-08,
    8.750627874791451325430828577550291173132606932489491650978024e-09,
    5.638473635810843029376963642806188780738425364694324846641801e-09,
    -7.167429275081674956250392820907257304489315331347262926580420e-10,
    -4.754207280832321963819557935464843642627576884386666887645887e-10,
    3.830468826216721628160165172614737318957786647293037946018968e-11,
    2.606522688312826422093936510197703125780060151935658173250167e-11,
    -1.001456631869681899515352955030379117902110811720682559024841e-12,
    -6.970967839322873084383232882845795920838117679540306239345441e-13
};

static const double coif10[60] = {
    1.232039860648044172279299405224082875252688628145893763395790e-07,
    -1.727295600804314762711191670132331791341141012661151334481794e-07,
    -2.511081517709769407961552319066117068245327429766666733928049e-06,
    3.658544046042552874351627900962366978528997860123373895778793e-06,
    2.436668276348995654567141715803151760606013118025710110377401e-05,
    -3.722543982033220274394394471108112000512191070987312940916252e-05,
    -1.497469279949762172542844959871018020917187701428092381975699e-04,
    2.428593780445920993243261353647084659003272232610773863151794e-04,
    6.540313182060373357098490399551418144208877502266803289923939e-04,
    -1.146063191568871560182378726825944083414392177714466101235875e-03,
    -2.159498802066973425125604747126061872540389369353813104148470e-03,
    4.198356898750693446005403941056755251392636143022056890090725e-03,
    5.598275450294209413911534747591226887261098750954757306041384e-03,
    -1.260095805571421166223679712763560488266840720081803933722030e-02,
    -1.168247265355164122837179014169010489974316491167398434017964e-02,
    3.285412351493396730558710291243592888780292689130164184828513e-02,
    1.996368405025267515773975348713442321055648689376329104127913e-02,
    -8.268628841732119164746367518414478774806468603519900610269584e-02,
    -2.827515877359611439071634624194631537427006948348240041241060e-02,
    3.072295399526691328155262418641709088201200529352508961256835e-01,
    5.334767884906935739862412544069281108017755758434730066259645e-01,
    3.148528691610333337017175073118974445216093370484743945217914e-01,
    -3.333673484145423901496513466029874130150784824285513273324710e-02,
    -8.973828824876461620729131462736908322846203417126514244115363e-02,
    2.804975877403786393498709022507676088711706121355859697479949e-02,
    3.882650078944377967276663169043218713908993336081868910959595e-02,
    -2.001823819093022389853947550669865247947997489243455484715571e-02,
    -1.715959267288721579873571730849821259719260958329570391883719e-02,
    1.216641740406415476398704875951061876471553491032831272757748e-02,
    7.287002668222967317616933795891484580054714258414483676611073e-03,
    -6.330873585683910945116572500327893678512281593087324528956428e-03,
    -2.982656931766638403179439090660944850553111019029093184942893e-03,
    2.842726797184870190904247769487845255184476983143715832751782e-03,
    1.194989100375986838359310533134452087293580850721847113981799e-03,
    -1.113592929026716220582486976294877082540073971334172516478939e-03,
    -4.665959023435428623124863826904417915798780109069842899171733e-04,
    3.854915448171689185378413676726041277985033697385268395398737e-04,
    1.722736489049248280735630694200218710841097211364504157426810e-04,
    -1.192039440276314665828270011182318354192262701354872993867613e-04,
    -5.799804551608036312810594836126727444524814767625101691871260e-05,
    3.303955116680311017462087431863046046226866796792060186027460e-05,
    1.735375106985050789058251093166552848987592399630060085283037e-05,
    -8.153689583347474492592925111165323709051679031024702923650800e-06,
    -4.549758014112714450559595397423919633198378184124113284457878e-06,
    1.765789027517910443710091424096930382691903616281733579514694e-06,
    1.034104799224371158938634470768393243015388383126290286799001e-06,
    -3.293437798170512614804826256037338836342628817269828495710584e-07,
    -2.008825017273243555706624872035287742407494975566457013532182e-07,
    5.172869892752758054467150354777867503503057315845912340980461e-08,
    3.267471887025676044777185234904542674259187212800749921513620e-08,
    -6.644210603985367626472026676249006537350458801953377329083388e-09,
    -4.326722848192605397244652678298064482622875881609332553562911e-09,
    6.694767246505386091091135023442214060965867621406459703646360e-10,
    4.478193476940001390457010880530025539995676698350272513506529e-10,
    -4.958883523601264332798684165341380344606385498242825617652400e-11,
    -3.396977896617646756144544675014526730130390520604930211664642e-11,
    2.399541927897950965447863495074910322552410081226759270966602e-12,
    1.679108441896732974456456195021048198285552771033686283521003e-12,
    -5.688326582012783070559328035350356616163807538811872116072541e-14,
    -4.057351322008929507004790032757210978914475486123608161502457e-14
};

static const double coif11[66] = {
    -3.002841270578181341151286551992204393218350582050331595402947e-08,
    4.122272295217427667266356780879156258114498741519631516920331e-08,
    6.724990810124306151562398023851821394904913501570357570065506e-07,
    -9.541360761072246280269422180608056185383175100955611359233360e-07,
    -7.202532561401011173077639437360684947826378373817567768549254e-06,
    1.063453162485789261522879856606560854907350313528134680849924e-05,
    4.909272375475312318494348591512780526373439988508822238418822e-05,
    -7.613184050961339338779773068101736230733664198546980476770618e-05,
    -2.390784892821873979161511417107775995428809136314600188678204e-04,
    3.943679383279311167583246559267867071442290440944189999981643e-04,
    8.853493370435959653823385085809947651235906281219659248568656e-04,
    -1.581686514051756535736270395427784669332587895176156014356356e-03,
    -2.590742746810224935563114329456904019725483278674143464295211e-03,
    5.149870818465970012295826228755714553228583889769273657074563e-03,
    6.145845672897527026066671647152239861924038000733917998375367e-03,
    -1.419393259281191647250120633508339618223543447429480888885451e-02,
    -1.203207413449444640173778009728050814154121296076212013667261e-02,
    3.486923068039100484429764121043168480751638068774959794674373e-02,
    1.969083876878412756398286849757105734674338626918970666727437e-02,
    -8.449176235282616008524865397153001199569198030863676330987291e-02,
    -2.718999570426944985720147291173682797300682913513030026037261e-02,
    3.080790765578655262603080397883700945436205022547475195838858e-01,
    5.318981263183423043747894675482113686160804211740980090048888e-01,
    3.153211471627422880186647707469009438784368258990685746892257e-01,
    -3.195766007061928848761664009258043321006644364041143491101353e-02,
    -9.124769812610534134560255473252823728312230005712995741316249e-02,
    2.745384767698916118757918507086581985709771313695467894960792e-02,
    4.069416828919323054368101934164054080633716017333800953161065e-02,
    -2.029580028320762422710091483209306643651315777764043153811346e-02,
    -1.877019961372162622019431440913198179510661354819964912645659e-02,
    1.296101510005597271938818874849814161448679262503765364267420e-02,
    8.380611488139723458203988762791929393262625838897962724368728e-03,
    -7.184830591966413369120495146871921423473823635538498945128881e-03,
    -3.609859908793898337842135309705990038242466823111355909651691e-03,
    3.480855644490575075872348257576088455972056599653664736521251e-03,
    1.519587122349825377958385405834371021723021014013798688930949e-03,
    -1.487476950041717485556524898256248562236771186568035610613046e-03,
    -6.266221323526552965740711288191177473318091288369663188353385e-04,
    5.669194750141824007727306877180277155664136228075876060283768e-04,
    2.483548169561553353815465009266736653140504966736322870003302e-04,
    -1.947562130464613262021359493483333444690328898219310892360572e-04,
    -9.182970970440133704016563635061723131880458403925476794220964e-05,
    6.068684153958316426511545635539425250648039719186543457181453e-05,
    3.088900022390199858477343797815906237906306891829977141838519e-05,
    -1.713715146668224571508596341585374794778243270230714316209873e-05,
    -9.308177549019247529116617185853630140339620355105237169582950e-06,
    4.350944607244218897315593155029012286145084684860699651446177e-06,
    2.489826198091695152209251895295085602387717821947484457226637e-06,
    -9.810226131982706356161587985205307909654994261025367440308819e-07,
    -5.860477917929613862778466907321520994203794368308376828863000e-07,
    1.935841542281693216091905456706113598339749921662496254592563e-07,
    1.199713894157469549665332933845486904444785619477457333399722e-07,
    -3.286315290852867745345772422742964814900623793534583508718160e-08,
    -2.102733655956564692974301994118024161699559390143901211493212e-08,
    4.697313377266824850774441291113868308618121213665395500265262e-09,
    3.091127346541495169376181275469205804231142302008934448079057e-09,
    -5.492931291038498819123339772152269159856609319161416280911575e-10,
    -3.705832674015332129353734352375876066802108394411578196121359e-10,
    5.043003455436262175385618694799102815016439527671656113021805e-11,
    3.478819684783597645372027295624442362976569795450758846730982e-11,
    -3.407139658961008818238265022607619145960800136177153397899540e-12,
    -2.397817050423233350570935216927526718398777501751418933159848e-12,
    1.505627237721835119751916336971964703936303188134672123458877e-13,
    1.078921592982513510792367677790040203074764526102391850430293e-13,
    -3.263876236830110071269528317803593334490309684938793753165837e-15,
    -2.377548488823423543125139291778920077224497814021891145988281e-15,
};

static const double coif12[72] = {

    7.332768095940030936479170284005726526900547838823690875511772e-09,
    -9.882788960094173321966534093367099204293628590516819427395258e-09,
    -1.789770690933674811780047200109598433010386971973430500672055e-07,
    2.482235718295242398333562594286195512316082783324858732252918e-07,
    2.096862898770904613564680054212556937787154121601629543533841e-06,
    -3.008997523302885197296541933722942006923516241629763269568385e-06,
    -1.569727511606401439531655092040058072751445499110659438977086e-05,
    2.347282446659006948884988502182500910534796840878776320306027e-05,
    8.432620671960839513456175441990492843161437159914478159180496e-05,
    -1.326465700910231806666155456982630828426491024439193157580022e-04,
    -3.461069031961523260598540665989975463806418435597964924450827e-04,
    5.801724592109383434087721189210231779950668973412340708531782e-04,
    1.128329952931538448642997807830809340563912699376148776100497e-03,
    -2.052978207989856468257756671236218003175082899217794093393105e-03,
    -2.998854718805581165102352821152213608341151163107294684246684e-03,
    6.087234661002692098937246236052456417362244403536977448648695e-03,
    6.618258248500649305698119745808151708404790949797235636697129e-03,
    -1.565844764466259880161945578291943699894979290174981301878469e-02,
    -1.229143532595011743051583354556862863433008454113067034110364e-02,
    3.663359030520896940491446552702346213307676159344592871561174e-02,
    1.940158311172772932244832520273815443263367988550797208188773e-02,
    -8.602421369693663560157681223178103065887873009243441444273671e-02,
    -2.622309610147498279896640360042796723325043337619084753451538e-02,
    3.087901191936800440665016928578559620370136549385877375418705e-01,
    5.305215546028922252760737677862501004955798466743925981589890e-01,
    3.157101125097144187884255713018906584467729236429617965600542e-01,
    -3.072728270411850832025519617026920233823691270616686168610683e-02,
    -9.252355517407954901591096183036942267909813631619741203079509e-02,
    2.685426980489526041311526911968158600872135457665305597171601e-02,
    4.231906584194910461336004572125137067789978502941750699346161e-02,
    -2.044171277278771763990740324636394218858904394719947562326197e-02,
    -2.023145788215137568397333973747514178822985181848891022548418e-02,
    1.360136074289957261858358098198061002684141161420690992513613e-02,
    9.426267566785124441978318969535742805562416304113067761168412e-03,
    -7.945708281838580982329878679291156067815423974682242221896984e-03,
    -4.244616137562462768873148640133872748101997290450405726506771e-03,
    4.099701294107222470709340142349077619300436310693220618061200e-03,
    1.865723923369076273507077887878603520484228647302554964863350e-03,
    -1.883095146836284131126558963690882906898471660722337699052360e-03,
    -8.050306441934266570411103136671969129999167330943811990532568e-04,
    7.774244768363498320173652454360791629397769478205798411568457e-04,
    3.372847962851042846971724663631080138529114657505401824166820e-04,
    -2.913208318185371820345028586920081498099751816901944041981459e-04,
    -1.340525559954698716569234001877227886256687091468901002407760e-04,
    9.982127673611594435680485331533411291981760920599082733502195e-05,
    4.937348728732130704294498684307923950522584256106944886949448e-05,
    -3.135036716312241024722693180559594774556478340860978546951440e-05,
    -1.658147773498940772484726369548058692747045913396859511266770e-05,
    8.993369381780249884815177697768622600696214964193331328463550e-06,
    5.029634518357754652723486937747064581822127275361432214835696e-06,
    -2.337864167664466494570925080931611944904459384149351585277512e-06,
    -1.368676631106717262348517888568825801321041470548296848624890e-06,
    5.450688318062644286926839313049161286349015828217309026636711e-07,
    3.316523922200394953147539061914190708715734274178414348860243e-07,
    -1.126508114108817402586366374763002332801656314726353024056859e-07,
    -7.087035083421007145362900821929976013240319835720189106840565e-08,
    2.036136552214949970328712725606531020556011014972233106738542e-08,
    1.319046726965912968164902682289037946241823978447803297147618e-08,
    -3.166087228464481990319888550523695148079523238750557992010525e-09,
    -2.104991149449598511738256933418413456831105806964526595267674e-09,
    4.146945431639040971236501326093312709747378328969720686955287e-10,
    2.821776098897944456514363403112985675647966425504805181915693e-10,
    -4.446983453406239471384232757602885317329111550023751533862821e-11,
    -3.089624808780293616300620576734718588630028421505260673719541e-11,
    3.747462360879854551489729939613548244791307999347455031364712e-12,
    2.653081274384644726529610503659130177381947905208003616541403e-12,
    -2.326413554174593468421370682954805683688973923244290583476656e-13,
    -1.675405289088832668656302656638525011959950143853262083260676e-13,
    9.457233677790636292829124094363707169383163229929980763002159e-15,
    6.917698090475727804437168736067302147625040054118083556555473e-15,
    -1.888230812078774308964339134901795324778762733781901178118498e-16,
    -1.401017335540698807093219863467094751924636611380629198255055e-16
};

static const double coif13[78] = {
    -1.793548393002850396214718676857202286659666424187461477734916e-09,
    2.378211185014311786110879108757436954350720046766491611553072e-09,
    4.738378239463632066007380653022285879027956504083662158044852e-08,
    -6.443042549205366017018471056937057609502890297774359019447097e-08,
    -6.027665210865901726655682284996751140817763415224888556619716e-07,
    8.442063562432018658923481573987170154629168292318465207460091e-07,
    4.916019713173742217516567081751095460187255617230097133575212e-06,
    -7.131597545851986841244858233956149652674565400158665885083228e-06,
    -2.887621717983862897214028125602451735414281976333647039362767e-05,
    4.370659106170444884665034460659021177081728764017693131624860e-05,
    1.301010589502334343162340998315752253281092458614582842230339e-04,
    -2.074517436437432445981843921824038136707694799228389255014386e-04,
    -4.675659350970496983228743630930465551287595323790670572391233e-04,
    7.958726623965077575159821003474228536032266651825968350366428e-04,
    1.376238684291389421260151294679793034786767219476646917508650e-03,
    -2.547925839943045924066603828685111513038130582703762656864035e-03,
    -3.380448382429659921946074764411430359936216225160144993981999e-03,
    6.999176747558747594441502446219623982632259493449887383625331e-03,
    7.025277873701559172377638256697086265819697238858476588169332e-03,
    -1.700403721911182477272019360628329604078467241211115244215388e-02,
    -1.248110041093927898560561468789729670710805348798639274049645e-02,
    3.818992191138900815386778979798299217649095657811074673392633e-02,
    1.910593122449920408487253901211150453954067290839567103918095e-02,
    -8.734123305433746527793626123316474698117075663794485992246041e-02,
    -2.535440772516182914289995632242460491764482405133781896194027e-02,
    3.093942491825020619708297996329932122162946840728332042771585e-01,
    5.293074910143676049377270336094486347453907972435992671221709e-01,
    3.160379244009616024122892848920228469630372079810266391126280e-01,
    -2.962163220306191371882270857478632946112548274231313092509283e-02,
    -9.361576444399936039733984609999327008143196158286264947803189e-02,
    2.626405247194866459650693584890446232517026215635389354877327e-02,
    4.374461075475828517065334688194008307695911637366798984389094e-02,
    -2.049134157245617651216272008653608168147661681048849172375451e-02,
    -2.155975349513035485872319905355436164781967949252709342828195e-02,
    1.411469715539851095863611028564527062867149436850684547048665e-02,
    1.042015012705080744964255765999888273943255976544095301354753e-02,
    -8.618241965220092705853657584595597150491673418403411002408392e-03,
    -4.878411229018901327291501121500987653856731550945133361152065e-03,
    4.689231319014487945639277139124902021493524893685817080310457e-03,
    2.228006944652422042461995816835639158465376266146249271470082e-03,
    -2.289300216009735975701956488620738834491684014507704186201093e-03,
    -9.995141996257751415873914459680403989351818715310372734784204e-04,
    1.011237075436018016549433779140494575127827962961245293854167e-03,
    4.381342083712223181655422780539654899256457840343499415845070e-04,
    -4.077800844935014982451276827385817092870017635124843071185474e-04,
    -1.844040745436007917646038575655224990196066190810660605304526e-04,
    1.512620828188999164176721097533404532449113024454664717717400e-04,
    7.298889914269074881614764112188558106800789316340412477261329e-05,
    -5.182922229726125421401881179344203187294861819954288822285889e-05,
    -2.672839739746518397507211250706440905085295052637683361350922e-05,
    1.639620996879063097510387977200493175731056979428359020893174e-05,
    8.963666558523048764112718546851367368030517279319460108445549e-06,
    -4.766464838000155192746436553819324857933144402808057358218020e-06,
    -2.735466156072759824467664346873929524570661158335396527180534e-06,
    1.263996241706422344627321843525682342021921638088785036829943e-06,
    7.554924136965026151745000640360811330632516737592082697244460e-07,
    -3.031372027194064857797292192738025195545051465615020648049694e-07,
    -1.876153353569459383609680347900216851365226014600160596860682e-07,
    6.512178154867684103162618794588474859582328666119453018835115e-08,
    4.155335593309412282776259416691605536103007067016402041304343e-08,
    -1.239596814755558143100692869308986827004606690663547726503092e-08,
    -8.126161332398177051328184604237057975154642800593274318357692e-09,
    2.063790114950702612059895249532033343340137201326403800942906e-09,
    1.385901944573589656765876273409079665095347609123824623959651e-09,
    -2.957224178853409095488876110988057035662351440629962056952036e-10,
    -2.029311934113340193298299122766339237347606272015477168851847e-10,
    3.571772829282367453820656456837216050596922160503361259637188e-11,
    2.499410377682389724091314347623318996671569127462013112818877e-11,
    -3.534890218851825710748828088218388133415831376956616696797412e-12,
    -2.517884527624136958659745558559312992706644927689162729125510e-12,
    2.751761925220381284379520354168022844435052022760463778480642e-13,
    1.992032170370043447922955009924406643179442598661368210363579e-13,
    -1.579660989452381199649841550208895174521301273998672860864606e-14,
    -1.160591617584672995112625842000957667465588057411636690187883e-14,
    5.944365969270122882900608066221185975352672745351687370149379e-16,
    4.427175531427877551269875594300908130209116956704562714053338e-16,
    -1.099853008896742180355036418837212161914020836623592509294684e-17,
    -8.294635939298345162821665414627459859677158780165562383375210e-18
};

static const double coif14[84] = {
    4.393123283287643046743977676392679809833123671791969857529554e-10,
    -5.740995333980705032482946798804467194492616738218164095035288e-10,
    -1.248927009234748737577807595315202881486499763243765085950971e-08,
    1.668938708534669081372944925855130859570883667030296283192275e-08,
    1.714224718466457012729254240886670886351889068326223437908249e-07,
    -2.350888489707823711598280869672645467891128086655653747378426e-07,
    -1.512829449512444398231569819651340325454203862764615475827846e-06,
    2.138843028494803759906484297432258680083063727505990520765838e-06,
    9.645189410175859129875925155219070194081512175741287765540858e-06,
    -1.413900766434184664705667161036292966126778120705989526301092e-05,
    -4.732386374886606069931596084808261069441902397080602834581236e-05,
    7.246665915416318653041426061722482786676878913517175624718883e-05,
    1.858717516638060924675018895058421783537325657288962327083514e-04,
    -3.002639346667087045056450058456524356780635529605230858304448e-04,
    -6.001981648119978322839905692747076742955350870879313864711891e-04,
    1.036704424281183769332139455241059670502046121189437350622494e-03,
    1.624019729102622430009407806264396637075700567293622239292888e-03,
    -3.056866654227471285910973139305702040302592610826831936007958e-03,
    -3.734374338375411359548223317593068662165508326760318658468879e-03,
    7.878974156362307531137246310651783340668870035859860685992973e-03,
    7.375794892775370758210034597418329565084392014608514718594919e-03,
    -1.824112316356241062692741179162085194558935295694349102012547e-02,
    -1.261644961352131486551701382111180567640901225706461078977920e-02,
    3.957213740580164637883912093052435411100033612855489943952285e-02,
    1.881013375897047936537530404984248905528105177371268733601760e-02,
    -8.848531745012440411869086432922856738256941097681738588710806e-02,
    -2.456831883802970358635588048610460583666496666472048714250036e-02,
    3.099141258514769597675719014297798938256245460277150903434604e-01,
    5.282263993480420420172683726700426615634588134049161997619639e-01,
    3.163176118910136471847844244749096048202155473691440639981631e-01,
    -2.862162705070505721847062425772578719710517153178747344517326e-02,
    -9.456098562568806415195259578154116428430268415200065101526812e-02,
    2.569051606124748855707310139514692975138855764331441058027644e-02,
    4.500466183802795354926526381495033532360504829689303112753676e-02,
    -2.047021532120584933449635791950977974026461349249053431599797e-02,
    -2.277014831481950414178652294664971699995306130850012029895679e-02,
    1.452361964346076643465698934593935628651549016326324569145137e-02,
    1.136128541937039082819287050631790459539185066230834792169280e-02,
    -9.209420947381265408066732190659507079985769827890316211408732e-03,
    -5.504969422021670816364481890406139436891358063842746391250159e-03,
    5.243747074749862954985821475569406094355637198674133818712452e-03,
    2.601674598427237622587421382305601947017094209097298433834035e-03,
    -2.697275841549192002303803931030430917736443859609363181433549e-03,
    -1.207776471356184889908600913725134183482141825908028112001291e-03,
    1.262622737054852256202461193382270265994430106051249583099008e-03,
    5.499311966045329956589364039445100979613421696316486920186038e-04,
    -5.422272627380624698839955658312215512244309349484916450185708e-04,
    -2.425184361340171436100075350681685661504913784969748899389596e-04,
    2.152149496400025476355694208172147853936641059086477755698365e-04,
    1.017555903218396117566574602158457196612571864784370737617320e-04,
    -7.935125388684553688655161870002244461407761128159434896618062e-05,
    -3.998277597219932662226493843559158394980004861650575475073792e-05,
    2.721796546580936689236981518793293817082739932936290854597108e-05,
    1.455457611510765292828084081835640403374221862390765492386559e-05,
    -8.665251169633984802148726995857663363842070697439942022312573e-06,
    -4.876002876304524386897822246961544519628259634511055566637692e-06,
    2.547630497377554690874245620888763103208548188356120375663417e-06,
    1.496291521165122830795751824395745883743697658779928306182602e-06,
    -6.871963182955767930114979093149987762546939511820085867823395e-07,
    -4.186054213338905216716743937485713975287167010801084873077525e-07,
    1.688323085510447043387946782295494719025294305992339556134599e-07,
    1.061608664532340347260400721455574451686604605297267706650340e-07,
    -3.748126052504886126462262044258726754387739289902914735975186e-08,
    -2.423790421295234792457591882318039900764790491363232906013318e-08,
    7.451944128021296384873848910815562162615603046168848323657834e-09,
    4.940829113027976152032509192714512864500880531926251999539656e-09,
    -1.313004290651891746787587770064129324937346786322188934177725e-09,
    -8.903006190166202390134120838864396696475015840820998020804486e-10,
    2.024339408983577773846670331538285833569817328592031229378977e-10,
    1.400716332649913272902011493738096845560270049348128272163217e-10,
    -2.687843639588594104182180535839092397502172743578767496300069e-11,
    -1.894326416951989057103502558801200560928286148276804817046860e-11,
    3.010449065125004813763195657162392547592377042598795440577733e-12,
    2.157549110009031291962129877779262524544188824969196959112637e-12,
    -2.765131213310687469788780767616475716770096486689988092675382e-13,
    -2.012365100006663638325338350119352463903184738855668161185981e-13,
    1.999559651890099694974046277400337493312467655228527142562129e-14,
    1.475855904098499257323658940826580143845487753742438097885182e-14,
    -1.067284385873308952698526062594338734883390319779112369515680e-15,
    -7.980454732861187078908378609623987154745352892645906516335706e-16,
    3.737960558513592434172838302437103561627990707012528832920768e-17,
    2.828736827706445597408742152789813333751511825684331285246309e-17,
    -6.443221017103948534696419228196665121335677484790396728190832e-19,
    -4.930480277882556230626331748820167974609092329853622801401922e-19
};

static const double coif15[90] = {
    -1.077388305690034925568519317607342584913833777614742512289616e-10,
    1.389587596342677833974329883522061230029685955993897881938371e-10,
    3.279393998194510988634872448273529681786105568152537128188232e-09,
    -4.314917428064750425742001408005222171763816486950620059656063e-09,
    -4.830480955281757774084367520164529040833872636548135345926328e-08,
    6.503442589750190197880818956936279316947206899423481669747179e-08,
    4.586202226530976939526210331832939590139698224375343383858299e-07,
    -6.341483528169214053400193756836695190022413814118163945498651e-07,
    -3.153995479916402549978249764513192992770609509053150015780527e-06,
    4.499804120432346419024095680248633407936442025574963153980269e-06,
    1.673949137596284707730217911852880032270066826000466620282769e-05,
    -2.478673953689472562808496884430051456931244781632393118270780e-05,
    -7.133403721727709318079068467834164023289442323885459167236538e-05,
    1.104625554161070296128803495762953054143520115043831806016786e-04,
    2.507269022498110858235302333943956572610456366436693548255909e-04,
    -4.101297424840067893342949874400724512592760895309517943587399e-04,
    -7.410047143677847135223722282750946641042780322618912256742384e-04,
    1.298007949810447142342589176001928848518589813981439840532480e-03,
    1.868002036616641522111287518387513439361411763383193116215989e-03,
    -3.572228252449254403963990114096416980805538949675814075573840e-03,
    -4.060845546989816000749383711188465103702344678606463573419343e-03,
    8.722973601634679156834244798678586473379340842447624760010613e-03,
    7.677632624610885216379589736677645849428563543670856456892832e-03,
    -1.937998629272623327245351651340032378963145895629830248355253e-02,
    -1.270912987080621205738379925710726654478701862241529402092432e-02,
    4.080738066060462170763876758586958836909785300855426384151542e-02,
    1.851809057592325149969383820293931082576986760496903450942510e-02,
    -8.948849897626424781521688387105939501138967888885573845565360e-02,
    -2.385247670399674430387895964623311594722573671400958395974253e-02,
    3.103664257008206645231191613171869470278587854279025003466673e-01,
    5.272557364814477983716848071901714167787737772153872785679251e-01,
    3.165587650075045121866443592930838541058283366878004666876859e-01,
    -2.771191753156204367193336798361266628380497625857187392479985e-02,
    -9.538675074777090841664081647061476726485316724852952970342536e-02,
    2.513756235583785139515577768765056710377747798734684870047976e-02,
    4.612595054152007266785799039333521785448817048938105661049367e-02,
    -2.039697130032260717744814776948275153234836586193746022325595e-02,
    -2.387610261602642412532087773123245712932154519493636933580111e-02,
    1.484665336652742504720315601481410278546028124094856659564326e-02,
    1.225045078722903548958708772567092776061773279630721958212731e-02,
    -9.727026988684677132100204448228164919286050721340500899358847e-03,
    -6.119790408666201569472108881793143052368912545364690258267249e-03,
    5.760546284765569349029577236247005441326471924211526110831333e-03,
    2.982639689783411796793051935450475228274526754982054792962250e-03,
    -3.100326747810698908670357861857946527442099277807566353764288e-03,
    -1.427598950056448560388339550760364489836477975524209634886824e-03,
    1.526288902864820487351136408213978592286541943096968601069530e-03,
    6.716807141163574375155812673929624759515069203426478824767004e-04,
    -6.923091961922026215168169239588952998971619503794813547976866e-04,
    -3.079753690537485675760336348529151045406687032785607559287851e-04,
    2.913765589242728312575501892839254155123375897451413662654452e-04,
    1.355892883935182989877008830272296210763299728475431240359051e-04,
    -1.144136457600174453829439526659056859019745676077854858593439e-04,
    -5.647366526527395256230283878165734782055526539079541637152500e-05,
    4.202814469137045804231920127700407277374824908280103138814183e-05,
    2.200691925199183606470694553320593703444346894670462898724267e-05,
    -1.443487126829503983751150823245149445739823542713233210642676e-05,
    -7.967044068720793687142187645523516573594381036006224439668751e-06,
    4.620529572512414091638085200280358471196525782083614164517925e-06,
    2.667271171141226202145908685103109359180536100787642097511661e-06,
    -1.371646007677255108283723404199936479217347063529498435611186e-06,
    -8.226257775777933787075210506019317042435483731184419620540542e-07,
    3.754707488446600189267042303254271529522879725934705336303128e-07,
    2.327460534735607792375886758810039203536663498026314128685740e-07,
    -9.419176207630012538547307468277369699399418776908683708933552e-08,
    -6.010901363192621272223652431192551853015388169886067903126279e-08,
    2.151040529867103646106333296464104096442766675831068812680466e-08,
    1.408663721444152455727196509866657267275259238167897249854527e-08,
    -4.438471764420978016551707826632529934591397495469950610230150e-09,
    -2.974872318653716789002892729632634360902192191395110942422961e-09,
    8.203736354629479986724051586473684219941281670765722417668819e-10,
    5.614906982011763291961026759329761169245818883997411717426871e-10,
    -1.344369082215606926514414754427875310059984578418456061162278e-10,
    -9.377858898150921640144384471918102613403240839501295781875527e-11,
    1.928845257086926701816336874687366534718638777936590534635229e-11,
    1.369011182657074022662376575505428888871791726174235399548870e-11,
    -2.384934840461770473688929159579828219691317417791483110727798e-12,
    -1.719778967631011393923929779685944798509748321140853638527672e-12,
    2.489379237039083598560573205355055108535626207998662168681925e-13,
    1.821429711569697474507499816102915339752696477298299745504329e-13,
    -2.132622020734041001406592439010728287329439597809749096351270e-14,
    -1.581480392919139876757416822279485491215786823105416373080481e-14,
    1.439584163156225165032712921349710482363055530913169445190637e-15,
    1.080870877608085873106724565550332487149959687560803170944524e-15,
    -7.178988398852581130761463819471838970960348029733518906208378e-17,
    -5.452456075593213060858973182314766684639894698667113525487884e-17,
    2.351146114661327422889598894161189162646924206890382575430334e-18,
    1.804873032260938513483901979969512893697634895683128101624000e-18,
    -3.793093840880643734719632714511071568608235299231836638122279e-20,
    -2.940897685979289093951754752537580047693528343881245635936847e-20
};

static const double coif16[96] = {
    2.645128227931334401194749017856094903839224759604187535975348e-11,
    -3.371203198492327805593180795398582247961552853458158404332701e-11,
    -8.582602955015851169989985547605915833769704579350018283905453e-10,
    1.113682743874024896922872631560006089322116513037553196433140e-09,
    1.350357555669007302187662129540475982947984508250679168402207e-08,
    -1.788542662216945768696335511240007863403120034978963421466759e-08,
    -1.372399767643349435868829666738563944471884881660198607643932e-07,
    1.861142798232072777457875152815716474831537306031011659394987e-07,
    1.012632916853084889127303847166996705778109099232881893357767e-06,
    -1.411391218929900871875409836854758822959995175269209988265964e-06,
    -5.780354906513060544090253238716694637224354416047278875195167e-06,
    8.319552925349192408642980463945086193729912325909182155797371e-06,
    2.656172937335282438452193694808438866757101525885555802877363e-05,
    -3.971488503705784806195172735954055483778562353132473949420383e-05,
    -1.009502477694915360037551527344779468467449497309371492607906e-04,
    1.580205984850860534574382720595384850826996029171338643269624e-04,
    3.235585956460851636390368611428001979093146497993005344305241e-04,
    -5.356766020478135601304959841352994036589791570941083598445719e-04,
    -8.873556546319179065472262621811119401586608437112185894641021e-04,
    1.575478281351532632439717118803866388929199851338529206979138e-03,
    2.105606651204431619054524746734864557623457229083947484722320e-03,
    -4.088191894353650561276869582147927639352373035186017974748492e-03,
    -4.360872292782198728376270454745337063294048791901294558973734e-03,
    9.529568586822569351636224042126142702339784399550130407812201e-03,
    7.937554616751835390855349461479084645184690272876975812761538e-03,
    -2.043028062273575036423529610008583445387419918322244014076844e-02,
    -1.276805798943377800134681508900535312774995954006995157514628e-02,
    4.191756976239118832120686348770409281363202997039966458608067e-02,
    1.823220381522837114361006424809362329414770645451392462449888e-02,
    -9.037537219708957768297195651417043288020180323453952191219619e-02,
    -2.319696593669936172505243393626168348704758643822052626742965e-02,
    3.107636928082792940641010571409269845541349652957861643505019e-01,
    5.263779607858160898968654079428530858936436413545697317378081e-01,
    3.167685981610576430160454114053628826549777499820093838585069e-01,
    -2.688002764399130975457186260346246083982252335484396969696049e-02,
    -9.611413622779535731607299381683923415585409438866982806312433e-02,
    2.460702264184366170433044783596672126378075093050623290409127e-02,
    4.712982774514209557951414612253477983062538454664569371378627e-02,
    -2.028535796367687397692592233666898932654122251735124185611584e-02,
    -2.488947090480479804467455088466028171550905433401327138474797e-02,
    1.509891466100476055081827485608676509355525843509560496666700e-02,
    1.308946215944044192535019406077351673456069366724670016932535e-02,
    -1.017883680802027111277390598900318783317279547588070199634251e-02,
    -6.719748071003458935775571012030544444136724430402220957162335e-03,
    6.238935489467385921022852560870393671940489207452244538094472e-03,
    3.367468902298941859370206452925695206983662336670801038560071e-03,
    -3.493551700182803646132263836442232208288107750606295145197587e-03,
    -1.656892742742846792662641923332649254682158396970453428791079e-03,
    1.797580707568879158693851162651173871535491718081800368907782e-03,
    8.023813584888036977682855962559617079147220211451113235479547e-04,
    -8.554898439358329127957776138969823280630767426284649193621767e-04,
    -3.803250541195305567062453832052319106641157897723285573667716e-04,
    3.790575215750319491406179627066855114144121793545714885424013e-04,
    1.743405946247572050905785206176608832450147638063148826743472e-04,
    -1.572439781316405883265776380849622489840291930399907686729106e-04,
    -7.624948592584476281257978178990423864516589094309239278548039e-05,
    6.128060596774506984228013865022369414316454857306786928520834e-05,
    3.146741963266068084535293135528229727240575644439933447074425e-05,
    -2.245327295507508821937104934719536410602401822650676684612963e-05,
    -1.216243182507186686293172059854826389902098520645342122978134e-05,
    7.720917364617828003652627867578175616946793895014715674935806e-06,
    4.381753178661449645296516382515863584261691809111376008934184e-06,
    -2.482767124382555232483399147672417058164552715497813674691440e-06,
    -1.466343698163771614780524818641106403735083198445173705132776e-06,
    7.432014174835368770981871416234950435655553162638884480980583e-07,
    4.542989331759269644535718413196051785388101460827659003970839e-07,
    -2.060699879933986058206760300398806699720723820737944360367781e-07,
    -1.298170865048347187816076578378402307087125075336611133383038e-07,
    5.264581641375993977610164728920521257086259803613235153366270e-08,
    3.406393391619068289195417747073909213446800676676765817676367e-08,
    -1.232185691522856953729451450757280208371851915642712294292861e-08,
    -8.166093443152050070015881861916421702970930717440939870025784e-09,
    2.625409011502555900921203294713520941212542142978903897341494e-09,
    1.777971486566651937150629742259638331676290922471013840418127e-09,
    -5.055670327267217324394512474432436609546961385665820073629149e-10,
    -3.491603047784209234885018449563067499142505674739795662842523e-10,
    8.724442027269310202793317644100398216549268990858642429012803e-11,
    6.134056646089015827615754897419277972739575045939973674430929e-11,
    -1.335550933533140093069772884082926341856038121300357216420744e-11,
    -9.545001301055681232254913151946668780573427381542116387716243e-12,
    1.791132091052570705585609717173338686203233385162086497884597e-12,
    1.299479612944140546458425080979636378248568664374156541007765e-12,
    -2.071542478174027600308270770710599707024301130761618879837078e-13,
    -1.523876419589572596433135600175398293765409391592690642809664e-13,
    2.024011564013098795384504309899456379953546444981404140928647e-14,
    1.508090533430543564179721448102668377867975040540446828012097e-14,
    -1.624316869855319846554453444493486257563508484962017834581348e-15,
    -1.224719779181149805165600696876308523367619824015832031858679e-15,
    1.027939569018525333323110570745538777072560620646729968488678e-16,
    7.836461641294003176782800892601140107677607857155479918585838e-17,
    -4.809623970748619654544928861639842603928203458148585584400867e-18,
    -3.704428468500691125388142845245956973807333959654959653370468e-18,
    1.479077310414365270465857340835402997778146774718460154867300e-19,
    1.150168591577669048697174066699145993471476194503387272230234e-19,
    -2.242407694525583556140058750006128491924916204038852290292985e-21,
    -1.759447752651847364048938898264828071373418617420955762135442e-21

};
static const double coif17[102] = {
    -6.500464389707599365209386548586351053892021378458138653304423e-12,
    8.195092532207287031929315133511822259455558174314608981943899e-12,
    2.239711179183031212893330219097909418305437894719629936823048e-10,
    -2.869947756536888954496920486027606890198732779175975385193469e-10,
    -3.748652869247186892176371615982066497362408141014325378716294e-09,
    4.892949218890121214706505923918329440794016605904313473814529e-09,
    4.060535996907768482760233032378242455655290824022613838518523e-08,
    -5.412800273268200651966255945372126329232373624996043338727765e-08,
    -3.199646878369895994575449942065193991181771279589753273012370e-07,
    4.369727725871308893880916467851990680716049438182815833632030e-07,
    1.954675426993304663744299195821942913722501797065421760283585e-06,
    -2.745513146733188764932399232387671708256227137296795085887882e-06,
    -9.634412972188383779747780077033215938910939748761965263335434e-06,
    1.398510128440620079969365258470536422184684456048432702271769e-05,
    3.936982972446342694180294131275967364969863087882012583523302e-05,
    -5.942171285371293607280496067664382735077180061970981330506268e-05,
    -1.360187671549120330733322490500505609713066081377747046659631e-04,
    2.151477677680517385918241110555023784071910115637214853600854e-04,
    4.031846985142799308758422861627631567915210555326501592195629e-04,
    -6.753056524717059857256997400254135291064617085196739372670596e-04,
    -1.037019038482154436516186769361377053347840390147975287354903e-03,
    1.865278399043969526599364729851177613810456808562792176901007e-03,
    2.335092479166843852619505691860960037151295448017402395490464e-03,
    -4.600362410545124310128593731153519957157915045164845600048444e-03,
    -4.635901516367530468266774633995064938864595165304482662269926e-03,
    1.029849878093488912016135237127927932437021681117450333050470e-02,
    8.161354844742695166287867590528306934013230497272094086439853e-03,
    -2.140083454707180990471957764441619892415141666259178396074188e-02,
    -1.280013066407215003063560801494039614508212222821778081264683e-02,
    4.292055861596262214973257156936151304056714777314126061614130e-02,
    1.795390690266262592020002418739607395852481356276768335967618e-02,
    -9.116513012907441746922273129338011409075492893988681588200024e-02,
    -2.259372514218996220695158554614204610976204851195489170219594e-02,
    3.111155431954246180810318298644662465180042749292370227009409e-01,
    5.255791920096491971882691260862499824764740619924123678110320e-01,
    3.169526419329032746356987532605638232939637480462148442295005e-01,
    -2.611571666365402171510563042119336240297149649416458785911946e-02,
    -9.675954853667507935197631998670465823029201914294383672129969e-02,
    2.409947009460623399471523812196780471148876929014194101062573e-02,
    4.803353218498044015416060073004473385881416575908077296603559e-02,
    -2.014561229992014297122142514992242081365435497050595479226927e-02,
    -2.582061660161950684110954569039206647948969052599286532681994e-02,
    1.529272640253308240084109219531756056459883643674183337274434e-02,
    1.388071397467832912373059115868453151221079623138136285750566e-02,
    -1.057221105345110246802817506341466648095287774314054180322389e-02,
    -7.302762653438232901227689142950547329538140829539078289487808e-03,
    6.679525479155670944534388145244600799904556867195771509687913e-03,
    3.753327910175261617061638983698597354605196849112621375976962e-03,
    -3.873502044238494209221075063035775273773018743760471430473527e-03,
    -1.893732606269440797293869629956889668362203362383552157238979e-03,
    2.072543024867895178559272835210161485056077281253166510139696e-03,
    9.410418747008923962829724903349867736584130840706583199324136e-04,
    -1.029236208596613388068225340603899734435060101885503041426292e-03,
    -4.591014336769466560134513688989777037625141817691452272446129e-04,
    4.773008760819621131157362935521176506345390013016953367673484e-04,
    2.178204082961996755621953357319268908714183698410765755733914e-04,
    -2.078309675413889310430737904787028903664009273165646558840663e-04,
    -9.929997042562200063281249722482587882228792457135570086630464e-05,
    8.530214005775875557344845401830936434184861259249422544962929e-05,
    4.303127018338600126871147843984523103940762542407178890295340e-05,
    -3.305701987257830145946843240723975476098534205446659095902282e-05,
    -1.758829650866963971776918784161632040379719078606077372721020e-05,
    1.208769577843697327040603409274814451217826643812352568992268e-05,
    6.746467675120962867147670937319739759733836479700116679771234e-06,
    -4.160355187316342019270990597595419877216228873682832991804769e-06,
    -2.420254904648915254014955740890841226083885744291947005091657e-06,
    1.342994249603250343064678689811024077753583828488987218045940e-06,
    8.097294554089061667504430331210249043842934823174097650144144e-07,
    -4.049444940788524829884048775715404594466969342774847971126013e-07,
    -2.518965167469206882003809454171378587968607051431751373150446e-07,
    1.135553617795151208855033374367694728763504841965654332076720e-07,
    7.261651917558697862566637161896385928154192436065001744633099e-08,
    -2.947980283978599533805321526934370923141985995773718687989809e-08,
    -1.932361910368835906234029388933678481624943182582035193495632e-08,
    7.050321193690929766855893625237523724477327743751448148520006e-09,
    4.725540898626838453733797773219853119997567959454100093653807e-09,
    -1.544890271654104814899353348226128082357337920194464818206503e-09,
    -1.056616142600432792887732860128652200494175165768667682069691e-09,
    3.082594659999300699574359679210901640096775130739951087627160e-10,
    2.147504833829198392113195006888709994262573995228896673552773e-10,
    -5.561286894620042086964943668161558452409502342704415471486767e-11,
    -3.940143496183440249847057247704879900653366187527397859030185e-11,
    8.995643580413385770110048797179920790859544777777746910312038e-12,
    6.472787221353141291937900214024339897450801045313739690950453e-12,
    -1.291529680835762905157565016413333440330657604408304192114484e-12,
    -9.426669651972276882689445158605509492502686267040217113580068e-13,
    1.625527264466347733962941488626801955710436180523082414371005e-13,
    1.202192377372031845627082725619201334357843245869818759928528e-13,
    -1.765518459644471564018808330096255160185888800253188548281582e-14,
    -1.321779015475951542862804164493083633990380882447565336832597e-14,
    1.621075287427611093636143991429535087675201571842337901672180e-15,
    1.227498087932053340876125326218310922521150243946862677354389e-15,
    -1.223432402366205698930334886661193082730379267225732134795174e-16,
    -9.362464931639200345906241184043712676525956780242932081695469e-17,
    7.286307451400376946525901096753718888478670661483802452725131e-18,
    5.631244920089436674573794405513798919334447045845529038034991e-18,
    -3.210669220728058803342344742726907080377349121830123276313938e-19,
    -2.504389307995414770751414118526232971373346964470499339128194e-19,
    9.305380936099856808127703965979897197171695480831630403884988e-21,
    7.321457794081650002123376485818067154029026613875178979778160e-21,
    -1.330546671429478643380151195298922795778536160301584472334406e-22,
    -1.055408614665356789775787814731337905514088897800715301399902e-22
};


static const double sym2[4] = {
    0.48296291314469025, 0.83651630373746899,
    0.22414386804185735, -0.12940952255092145 };


static const double sym3[6] = {
    0.33267055295095688, 0.80689150931333875,
    0.45987750211933132, -0.13501102001039084,
    -0.085441273882241486, 0.035226291882100656 };

static const double sym4[8] = {
    0.032223100604042702, -0.012603967262037833,
    -0.099219543576847216, 0.29785779560527736,
    0.80373875180591614, 0.49761866763201545,
    -0.02963552764599851, -0.075765714789273325 };

static const double sym5[10] = {
    0.019538882735286728, -0.021101834024758855,
    -0.17532808990845047, 0.016602105764522319,
    0.63397896345821192, 0.72340769040242059,
    0.1993975339773936, -0.039134249302383094,
    0.029519490925774643, 0.027333068345077982 };

static const double sym6[12] = {
    -0.007800708325034148, 0.0017677118642428036,
    0.044724901770665779, -0.021060292512300564,
    -0.072637522786462516, 0.3379294217276218,
    0.787641141030194, 0.49105594192674662,
    -0.048311742585632998, -0.11799011114819057,
    0.0034907120842174702, 0.015404109327027373 };

static const double sym7[14] = {
    0.010268176708511255, 0.0040102448715336634,
    -0.10780823770381774, -0.14004724044296152,
    0.28862963175151463, 0.76776431700316405,
    0.5361019170917628, 0.017441255086855827,
    -0.049552834937127255, 0.067892693501372697,
    0.03051551316596357, -0.01263630340325193,
    -0.0010473848886829163, 0.0026818145682578781 };

static const double sym8[16] = {
    0.0018899503327594609, -0.0003029205147213668,
    -0.014952258337048231, 0.0038087520138906151,
    0.049137179673607506, -0.027219029917056003,
    -0.051945838107709037, 0.3644418948353314,
    0.77718575170052351, 0.48135965125837221,
    -0.061273359067658524, -0.14329423835080971,
    0.0076074873249176054, 0.031695087811492981,
    -0.00054213233179114812, -0.0033824159510061256 };

static const double sym9[18] = {
    0.0010694900329086053, -0.00047315449868008311,
    -0.010264064027633142, 0.0088592674934004842,
    0.06207778930288603, -0.018233770779395985,
    -0.19155083129728512, 0.035272488035271894,
    0.61733844914093583, 0.717897082764412,
    0.238760914607303, -0.054568958430834071,
    0.00058346274612580684, 0.03022487885827568,
    -0.01152821020767923, -0.013271967781817119,
    0.00061978088898558676, 0.0014009155259146807 };

static const double sym10[20] = {
    -0.00045932942100465878, 5.7036083618494284e-005,
    0.0045931735853118284, -0.00080435893201654491,
    -0.02035493981231129, 0.0057649120335819086,
    0.049994972077376687, -0.0319900568824278,
    -0.035536740473817552, 0.38382676106708546,
    0.7695100370211071, 0.47169066693843925,
    -0.070880535783243853, -0.15949427888491757,
    0.011609893903711381, 0.045927239231092203,
    -0.0014653825813050513, -0.0086412992770224222,
    9.5632670722894754e-005, 0.00077015980911449011 };

static const double sym11[22] = {
    0.00048926361026192387, 0.00011053509764272153,
    -0.0063896036664548919, -0.0020034719001093887,
    0.043000190681552281, 0.035266759564466552,
    -0.14460234370531561, -0.2046547944958006,
    0.23768990904924897, 0.73034354908839572,
    0.57202297801008706, 0.097198394458909473,
    -0.022832651022562687, 0.069976799610734136,
    0.0370374159788594, -0.024080841595864003,
    -0.0098579348287897942, 0.0065124956747714497,
    0.00058835273539699145, -0.0017343662672978692,
    -3.8795655736158566e-005, 0.00017172195069934854 };

static const double sym12[24] = {
    -0.00017906658697508691, -1.8158078862617515e-005,
    0.0023502976141834648, 0.00030764779631059454,
    -0.014589836449234145, -0.0026043910313322326,
    0.057804179445505657, 0.01530174062247884,
    -0.17037069723886492, -0.07833262231634322,
    0.46274103121927235, 0.76347909778365719,
    0.39888597239022, -0.022162306170337816,
    -0.035848830736954392, 0.049179318299660837,
    0.0075537806116804775, -0.024220722675013445,
    -0.0014089092443297553, 0.007414965517654251,
    0.00018021409008538188, -0.0013497557555715387,
    -1.1353928041541452e-005, 0.00011196719424656033 };

static const double sym13[26] = {
    7.0429866906944016e-005, 3.6905373423196241e-005,
    -0.0007213643851362283, 0.00041326119884196064,
    0.0056748537601224395, -0.0014924472742598532,
    -0.020749686325515677, 0.017618296880653084,
    0.092926030899137119, 0.0088197576704205465,
    -0.14049009311363403, 0.11023022302137217,
    0.64456438390118564, 0.69573915056149638,
    0.19770481877117801, -0.12436246075153011,
    -0.059750627717943698, 0.013862497435849205,
    -0.017211642726299048, -0.02021676813338983,
    0.0052963597387250252, 0.0075262253899680996,
    -0.00017094285853022211, -0.0011360634389281183,
    -3.5738623648689009e-005, 6.8203252630753188e-005 };

static const double sym14[28] = {
    4.4618977991475265e-005, 1.9329016965523917e-005,
    -0.00060576018246643346, -7.3214213567023991e-005,
    0.0045326774719456481, 0.0010131419871842082,
    -0.019439314263626713, -0.0023650488367403851,
    0.069827616361807551, 0.025898587531046669,
    -0.15999741114652205, -0.058111823317717831,
    0.47533576263420663, 0.75997624196109093,
    0.39320152196208885, -0.035318112114979733,
    -0.057634498351326995, 0.037433088362853452,
    0.0042805204990193782, -0.029196217764038187,
    -0.0027537747912240711, 0.010037693717672269,
    0.00036647657366011829, -0.002579441725933078,
    -6.2865424814776362e-005, 0.00039843567297594335,
    1.1210865808890361e-005, -2.5879090265397886e-005 };

static const double sym15[30] = {
    2.8660708525318081e-005, 2.1717890150778919e-005,
    -0.00040216853760293483, -0.00010815440168545525,
    0.003481028737064895, 0.0015261382781819983,
    -0.017171252781638731, -0.0087447888864779517,
    0.067969829044879179, 0.068393310060480245,
    -0.13405629845625389, -0.1966263587662373,
    0.2439627054321663, 0.72184302963618119,
    0.57864041521503451, 0.11153369514261872,
    -0.04108266663538248, 0.040735479696810677,
    0.021937642719753955, -0.038876716876833493,
    -0.019405011430934468, 0.010079977087905669,
    0.003423450736351241, -0.0035901654473726417,
    -0.00026731644647180568, 0.0010705672194623959,
    5.5122547855586653e-005, -0.00016066186637495343,
    -7.3596667989194696e-006, 9.7124197379633478e-006 };

static const double sym16[32] = {
    -1.0797982104319795e-005, -5.3964831793152419e-006,
    0.00016545679579108483, 3.656592483348223e-005,
    -0.0013387206066921965, -0.00022211647621176323,
    0.0069377611308027096, 0.001359844742484172,
    -0.024952758046290123, -0.0035102750683740089,
    0.078037852903419913, 0.03072113906330156,
    -0.15959219218520598, -0.054040601387606135,
    0.47534280601152273, 0.75652498787569711,
    0.39712293362064416, -0.034574228416972504,
    -0.066983049070217779, 0.032333091610663785,
    0.0048692744049046071, -0.031051202843553064,
    -0.0031265171722710075, 0.012666731659857348,
    0.00071821197883178923, -0.0038809122526038786,
    -0.0001084456223089688, 0.00085235471080470952,
    2.8078582128442894e-005, -0.00010943147929529757,
    -3.1135564076219692e-006, 6.2300067012207606e-006 };

static const double sym17[34] = {
    3.7912531943321266e-006, -2.4527163425832999e-006,
    -7.6071244056051285e-005, 2.5207933140828779e-005,
    0.0007198270642148971, 5.8400428694052584e-005,
    -0.0039323252797979023, -0.0019054076898526659,
    0.012396988366648726, 0.0099529825235095976,
    -0.01803889724191924, -0.0072616347509287674,
    0.016158808725919346, -0.086070874720733381,
    -0.15507600534974825, 0.18053958458111286,
    0.68148899534492502, 0.65071662920454565,
    0.14239835041467819, -0.11856693261143636,
    0.0172711782105185, 0.10475461484223211,
    0.017903952214341119, -0.033291383492359328,
    -0.0048192128031761478, 0.010482366933031529,
    0.0008567700701915741, -0.0027416759756816018,
    -0.00013864230268045499, 0.0004759963802638669,
    -1.3506383399901165e-005, -6.2937025975541919e-005,
    2.7801266938414138e-006, 4.297343327345983e-006 };

static const double sym18[36] = {
    -1.5131530692371587e-006, 7.8472980558317646e-007,
    2.9557437620930811e-005, -9.858816030140058e-006,
    -0.00026583011024241041, 4.7416145183736671e-005,
    0.0014280863270832796, -0.00018877623940755607,
    -0.0052397896830266083, 0.0010877847895956929,
    0.015012356344250213, -0.0032607442000749834,
    -0.031712684731814537, 0.0062779445543116943,
    0.028529597039037808, -0.073799207290607169,
    -0.032480573290138676, 0.40148386057061813,
    0.75362914010179283, 0.47396905989393956,
    -0.052029158983952786, -0.15993814866932407,
    0.033995667103947358, 0.084219929970386548,
    -0.0050770851607570529, -0.030325091089369604,
    0.0016429863972782159, 0.0095021643909623654,
    -0.00041152110923597756, -0.0023138718145060992,
    7.0212734590362685e-005, 0.00039616840638254753,
    -1.4020992577726755e-005, -4.5246757874949856e-005,
    1.354915761832114e-006, 2.6126125564836423e-006 };

static const double sym19[38] = {
    1.7509367995348687e-006, 2.0623170632395688e-006,
    -2.8151138661550245e-005, -1.6821387029373716e-005,
    0.00027621877685734072, 0.00012930767650701415,
    -0.0017049602611649971, -0.00061792232779831076,
    0.0082622369555282547, 0.0043193518748949689,
    -0.027709896931311252, -0.016908234861345205,
    0.084072676279245043, 0.093630843415897141,
    -0.11624173010739675, -0.17659686625203097,
    0.25826616923728363, 0.71955552571639425,
    0.57814494533860505, 0.10902582508127781,
    -0.067525058040294086, 0.0089545911730436242,
    0.0070155738571741596, -0.046635983534938946,
    -0.022651993378245951, 0.015797439295674631,
    0.0079684383206133063, -0.005122205002583014,
    -0.0011607032572062486, 0.0021214250281823303,
    0.00015915804768084938, -0.00063576451500433403,
    -4.6120396002105868e-005, 0.0001155392333357879,
    8.8733121737292863e-006, -1.1880518269823984e-005,
    -6.4636513033459633e-007, 5.4877327682158382e-007 };

static const double sym20[40] = {
    -6.3291290447763946e-007, -3.2567026420174407e-007,
    1.22872527779612e-005, 4.5254222091516362e-006,
    -0.00011739133516291466, -2.6615550335516086e-005,
    0.00074761085978205719, 0.00012544091723067259,
    -0.0034716478028440734, -0.0006111263857992088,
    0.012157040948785737, 0.0019385970672402002,
    -0.035373336756604236, -0.0068437019650692274,
    0.088919668028199561, 0.036250951653933078,
    -0.16057829841525254, -0.051088342921067398,
    0.47199147510148703, 0.75116272842273002,
    0.40583144434845059, -0.029819368880333728,
    -0.078994344928398158, 0.025579349509413946,
    0.0081232283560096815, -0.031629437144957966,
    -0.0033138573836233591, 0.017004049023390339,
    0.0014230873594621453, -0.0066065857990888609,
    -0.0003052628317957281, 0.0020889947081901982,
    7.2159911880740349e-005, -0.00049473109156726548,
    -1.928412300645204e-005, 7.992967835772481e-005,
    3.0256660627369661e-006, -7.919361411976999e-006,
    -1.9015675890554106e-007, 3.695537474835221e-007 };


static const double h1[10] = { 0.0, 0.0, 0.0, 0.0,
0.70710678118654752440084436210,
0.70710678118654752440084436210,
0.0, 0.0, 0.0, 0.0 };

static const double hm1_11[2] = { 0.70710678118654752440084436210,
0.70710678118654752440084436210 };

static const double hm1_13[6] = { -0.0883883476483184405501055452631,
0.0883883476483184405501055452631,
0.70710678118654752440084436210,
0.70710678118654752440084436210,
0.0883883476483184405501055452631,
-0.0883883476483184405501055452631 };

static const double hm1_15[10] = { 0.0165728151840597076031447897368,
-0.0165728151840597076031447897368,
-0.1215339780164378557563951247368,
0.1215339780164378557563951247368,
0.70710678118654752440084436210,
0.70710678118654752440084436210,
0.1215339780164378557563951247368,
-0.1215339780164378557563951247368,
-0.0165728151840597076031447897368,
0.0165728151840597076031447897368 };


static const double h2[18] = { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
0.3535533905932737622004221810524,
0.7071067811865475244008443621048,
0.3535533905932737622004221810524,
0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
0.0 };

static const double hm2_22[6] = { -0.1767766952966368811002110905262,
0.3535533905932737622004221810524,
1.0606601717798212866012665431573,
0.3535533905932737622004221810524,
-0.1767766952966368811002110905262,
0.0 };

static const double hm2_24[10] = { 0.0331456303681194152062895794737,
-0.0662912607362388304125791589473,
-0.1767766952966368811002110905262,
0.4198446513295125926130013399998,
0.9943689110435824561886873842099,
0.4198446513295125926130013399998,
-0.1767766952966368811002110905262,
-0.0662912607362388304125791589473,
0.0331456303681194152062895794737,
0.0 };

static const double hm2_26[14] = { -0.0069053396600248781679769957237,
0.0138106793200497563359539914474,
0.0469563096881691715422435709210,
-0.1077232986963880994204411332894,
-0.1698713556366120029322340948025,
0.4474660099696121052849093228945,
0.9667475524034829435167794013152,
0.4474660099696121052849093228945,
-0.1698713556366120029322340948025,
-0.1077232986963880994204411332894,
0.0469563096881691715422435709210,
0.0138106793200497563359539914474,
-0.0069053396600248781679769957237,
0.0 };

static const double hm2_28[18] = { 0.0015105430506304420992449678146,
-0.0030210861012608841984899356291,
-0.0129475118625466465649568669819,
0.0289161098263541773284036695929,
0.0529984818906909399392234421792,
-0.1349130736077360572068505539514,
-0.1638291834340902345352542235443,
0.4625714404759165262773590010400,
0.9516421218971785225243297231697,
0.4625714404759165262773590010400,
-0.1638291834340902345352542235443,
-0.1349130736077360572068505539514,
0.0529984818906909399392234421792,
0.0289161098263541773284036695929,
-0.0129475118625466465649568669819,
-0.0030210861012608841984899356291,
0.0015105430506304420992449678146,
0.0 };

static const double h3[20] = { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
0.1767766952966368811002110905262,
0.5303300858899106433006332715786,
0.5303300858899106433006332715786,
0.1767766952966368811002110905262,
0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
static const double hm3_31[4] = { -0.3535533905932737622004221810524,
1.0606601717798212866012665431573,
1.0606601717798212866012665431573,
-0.3535533905932737622004221810524 };

static const double hm3_33[8] = { 0.0662912607362388304125791589473,
-0.1988737822087164912377374768420,
-0.1546796083845572709626847042104,
0.9943689110435824561886873842099,
0.9943689110435824561886873842099,
-0.1546796083845572709626847042104,
-0.1988737822087164912377374768420,
0.0662912607362388304125791589473 };

static const double hm3_35[12] = { -0.0138106793200497563359539914474,
0.0414320379601492690078619743421,
0.0524805814161890740766251675000,
-0.2679271788089652729175074340788,
-0.0718155324642587329469607555263,
0.9667475524034829435167794013152,
0.9667475524034829435167794013152,
-0.0718155324642587329469607555263,
-0.2679271788089652729175074340788,
0.0524805814161890740766251675000,
0.0414320379601492690078619743421,
-0.0138106793200497563359539914474 };

static const double hm3_37[16] = { 0.0030210861012608841984899356291,
-0.0090632583037826525954698068873,
-0.0168317654213106405344439270765,
0.0746639850740189951912512662623,
0.0313329787073628846871956180962,
-0.3011591259228349991008967259990,
-0.0264992409453454699696117210896,
0.9516421218971785225243297231697,
0.9516421218971785225243297231697,
-0.0264992409453454699696117210896,
-0.3011591259228349991008967259990,
0.0313329787073628846871956180962,
0.0746639850740189951912512662623,
-0.0168317654213106405344439270765,
-0.0090632583037826525954698068873,
0.0030210861012608841984899356291 };


static const double hm3_39[20] = { -0.0006797443727836989446602355165,
0.0020392331183510968339807065496,
0.0050603192196119810324706421788,
-0.0206189126411055346546938106687,
-0.0141127879301758447558029850103,
0.0991347824942321571990197448581,
0.0123001362694193142367090236328,
-0.3201919683607785695513833204624,
0.0020500227115698857061181706055,
0.9421257006782067372990864259380,
0.9421257006782067372990864259380,
0.0020500227115698857061181706055,
-0.3201919683607785695513833204624,
0.0123001362694193142367090236328,
0.0991347824942321571990197448581,
-0.0141127879301758447558029850103,
-0.0206189126411055346546938106687,
0.0050603192196119810324706421788,
0.0020392331183510968339807065496,
-0.0006797443727836989446602355165 };


static const double h4[10] = {
    0.0, -0.064538882628697058,
    -0.040689417609164058, 0.41809227322161724,
    0.7884856164055829, 0.41809227322161724,
    -0.040689417609164058, -0.064538882628697058,
    0.0, 0.0 };

static const double hm4_44[10] = {
    0.03782845550726404, -0.023849465019556843,
    -0.11062440441843718, 0.37740285561283066,
    0.85269867900889385, 0.37740285561283066,
    -0.11062440441843718, -0.023849465019556843,
    0.03782845550726404, 0.0 };

static const double h5[12] = {
    0.013456709459118716, -0.0026949668801115071,
    -0.13670658466432914, -0.093504697400938863,
    0.47680326579848425, 0.89950610974864842,
    0.47680326579848425, -0.093504697400938863,
    -0.13670658466432914, -0.0026949668801115071,
    0.013456709459118716, 0.0 };

static const double hm5_55[12] = {
    0.0, 0.03968708834740544,
    0.0079481086372403219, -0.054463788468236907,
    0.34560528195603346, 0.73666018142821055,
    0.34560528195603346, -0.054463788468236907,
    0.0079481086372403219, 0.03968708834740544,
    0.0, 0.0 };

static const double h6[18] = {
    0.0, 0.0,
    0.0, 0.014426282505624435,
    0.014467504896790148, -0.078722001062628819,
    -0.040367979030339923, 0.41784910915027457,
    0.75890772945365415, 0.41784910915027457,
    -0.040367979030339923, -0.078722001062628819,
    0.014467504896790148, 0.014426282505624435,
    0.0, 0.0,
    0.0, 0.0 };

static const double hm6_68[18] = {
    0.0019088317364812906, -0.0019142861290887667,
    -0.016990639867602342, 0.01193456527972926,
    0.04973290349094079, -0.077263173167204144,
    -0.09405920349573646, 0.42079628460982682,
    0.82592299745840225, 0.42079628460982682,
    -0.09405920349573646, -0.077263173167204144,
    0.04973290349094079, 0.01193456527972926,
    -0.016990639867602342, -0.0019142861290887667,
    0.0019088317364812906, 0.0 };

static const double meyer[102] = {
    -0.000001509740857,
    0.000001278766757,
    0.000000449585560,
    -0.000002096568870,
    0.000001723223554,
    0.000000698082276,
    -0.000002879408033,
    0.000002383148395,
    0.000000982515602,
    -0.000004217789186,
    0.000003353501538,
    0.000001674721859,
    -0.000006034501342,
    0.000004837555802,
    0.000002402288023,
    -0.000009556309846,
    0.000007216527695,
    0.000004849078300,
    -0.000014206928581,
    0.000010503914271,
    0.000006187580298,
    -0.000024438005846,
    0.000020106387691,
    0.000014993523600,
    -0.000046428764284,
    0.000032341311914,
    0.000037409665760,
    -0.000102779005085,
    0.000024461956845,
    0.000149713515389,
    -0.000075592870255,
    -0.000139913148217,
    -0.000093512893880,
    0.000161189819725,
    0.000859500213762,
    -0.000578185795273,
    -0.002702168733939,
    0.002194775336459,
    0.006045510596456,
    -0.006386728618548,
    -0.011044641900539,
    0.015250913158586,
    0.017403888210177,
    -0.032094063354505,
    -0.024321783959519,
    0.063667300884468,
    0.030621243943425,
    -0.132696615358862,
    -0.035048287390595,
    0.444095030766529,
    0.743751004903787,
    0.444095030766529,
    -0.035048287390595,
    -0.132696615358862,
    0.030621243943425,
    0.063667300884468,
    -0.024321783959519,
    -0.032094063354505,
    0.017403888210177,
    0.015250913158586,
    -0.011044641900539,
    -0.006386728618548,
    0.006045510596456,
    0.002194775336459,
    -0.002702168733939,
    -0.000578185795273,
    0.000859500213762,
    0.000161189819725,
    -0.000093512893880,
    -0.000139913148217,
    -0.000075592870255,
    0.000149713515389,
    0.000024461956845,
    -0.000102779005085,
    0.000037409665760,
    0.000032341311914,
    -0.000046428764284,
    0.000014993523600,
    0.000020106387691,
    -0.000024438005846,
    0.000006187580298,
    0.000010503914271,
    -0.000014206928581,
    0.000004849078300,
    0.000007216527695,
    -0.000009556309846,
    0.000002402288023,
    0.000004837555802,
    -0.000006034501342,
    0.000001674721859,
    0.000003353501538,
    -0.000004217789186,
    0.000000982515602,
    0.000002383148395,
    -0.000002879408033,
    0.000000698082276,
    0.000001723223554,
    -0.000002096568870,
    0.000000449585560,
    0.000001278766757,
    -0.000001509740857,
    0.0};

int filtlength(const char* name) {
    int len = strlen(name);
    int i = 0;
    char *new_str = NULL;
    int N = 0;
	if (!strcmp(name,"haar") || !strcmp(name,"db1")) {
		return 2;
	}
    else if (len > 2 && strstr(name, "db") != NULL)
    {
        new_str = (char*)malloc(sizeof(char)*(len-2 + 1));
        for (i = 2; i < len + 1; i++)
            new_str[i - 2] = name[i];

        N = atoi(new_str);
        free(new_str);
        if (N>38)
        {
            printf("\n Filter Not in Database \n");
            return -1;
        }

        return N * 2;
    }
	else if (!strcmp(name,"bior1.1")){
		return 2;
	}

	else if (!strcmp(name,"bior1.3")){
		return 6;
	}

	else if (!strcmp(name,"bior1.5")){
		return 10;
	}

	else if (!strcmp(name,"bior2.2")){
		return 6;
	}

	else if (!strcmp(name,"bior2.4")){
		return 10;
	}

	else if (!strcmp(name,"bior2.6")){
		return 14;
	}
	else if (!strcmp(name,"bior2.8")){
		return 18;
	}

	else if (!strcmp(name,"bior3.1")){
		return 4;
	}
	else if (!strcmp(name,"bior3.3")){
		return 8;
	}
	else if (!strcmp(name,"bior3.5")){
		return 12;
	}

	else if (!strcmp(name,"bior3.7")){
		return 16;
	}
	else if (!strcmp(name,"bior3.9")){
		return 20;
	}
	else if (!strcmp(name,"bior4.4")){
		return 10;
	}
	else if (!strcmp(name,"bior5.5")){
		return 12;
	}
	else if (!strcmp(name,"bior6.8")){
		return 18;
	}
    else if (!strcmp(name, "rbior1.1")){
        return 2;
    }

    else if (!strcmp(name, "rbior1.3")){
        return 6;
    }

    else if (!strcmp(name, "rbior1.5")){
        return 10;
    }

    else if (!strcmp(name, "rbior2.2")){
        return 6;
    }

    else if (!strcmp(name, "rbior2.4")){
        return 10;
    }

    else if (!strcmp(name, "rbior2.6")){
        return 14;
    }
    else if (!strcmp(name, "rbior2.8")){
        return 18;
    }

    else if (!strcmp(name, "rbior3.1")){
        return 4;
    }
    else if (!strcmp(name, "rbior3.3")){
        return 8;
    }
    else if (!strcmp(name, "rbior3.5")){
        return 12;
    }

    else if (!strcmp(name, "rbior3.7")){
        return 16;
    }
    else if (!strcmp(name, "rbior3.9")){
        return 20;
    }
    else if (!strcmp(name, "rbior4.4")){
        return 10;
    }
    else if (!strcmp(name, "rbior5.5")){
        return 12;
    }
    else if (!strcmp(name, "rbior6.8")){
        return 18;
    }
    else if (len > 4 && strstr(name, "coif") != NULL)
    {
        new_str = (char*)malloc(sizeof(char)*(len - 4 + 1));
        for (i = 4; i < len + 1; i++)
            new_str[i - 4] = name[i];

        N = atoi(new_str);
        free(new_str);
        if (N>17)
        {
            printf("\n Filter Not in Database \n");
            return -1;
        }

        return N * 6;
    }
    else if (len > 3 && strstr(name, "sym") != NULL)
    {
        new_str = (char*)malloc(sizeof(char)*(len - 3 + 1));
        for (i = 3; i < len + 1; i++)
            new_str[i - 3] = name[i];

        N = atoi(new_str);
        free(new_str);
        if (N>20 || N < 2)
        {
            printf("\n Filter Not in Database \n");
            return -1;
        }

        return N * 2;
    }
    else if (!strcmp(name, "meyer")){
        return 102;
    }
	else {
		printf("\n Filter Not in Database \n");
		return -1;
	}

}

void copy_reverse(const double *in, int N,double *out)
{
    int count = 0;
    for (count = 0; count < N; count++)
        out[count] = in[N - count - 1];
}

void qmf_wrev(const double *in, int N, double *out)
{
    double *sigOutTemp;
    sigOutTemp = (double*)malloc(N*sizeof(double));

    qmf_even(in, N, sigOutTemp);
    copy_reverse(sigOutTemp, N, out);

    free(sigOutTemp);
    return;
}

void qmf_even(const double *in, int N,double *out)
{
    int count = 0;
    for (count = 0; count < N; count++)
    {
        out[count] = in[N - count - 1];
        if (count % 2 != 0)
        {
            out[count] = -1 * out[count];
        }
    }
}
void copy(const double *in, int N, double *out)
{
    int count = 0;
    for (count = 0; count < N; count++)
        out[count] = in[count];
}

int filtcoef(const char* name, double *lp1, double *hp1, double *lp2, double *hp2) {
    int i = 0; 
    int N = filtlength(name);
	if (!strcmp(name,"haar") || !strcmp(name,"db1")) {
        copy_reverse(db1, N, lp1);
        qmf_wrev(db1, N, hp1);
        copy(db1, N, lp2);
        qmf_even(db1, N, hp2);

		return N;
	}
	else if (!strcmp(name,"db2")){
        copy_reverse(db2, N, lp1);
        qmf_wrev(db2, N, hp1);
        copy(db2, N, lp2);
        qmf_even(db2, N, hp2);

        return N;
	}
	else if (!strcmp(name,"db3")){
        copy_reverse(db3, N, lp1);
        qmf_wrev(db3, N, hp1);
        copy(db3, N, lp2);
        qmf_even(db3, N, hp2);

        return N;
	}
	else if (!strcmp(name,"db4")){
        copy_reverse(db4, N, lp1);
        qmf_wrev(db4, N, hp1);
        copy(db4, N, lp2);
        qmf_even(db4, N, hp2);

        return N;
	}
	else if (!strcmp(name,"db5")){
        copy_reverse(db5, N, lp1);
        qmf_wrev(db5, N, hp1);
        copy(db5, N, lp2);
        qmf_even(db5, N, hp2);

        return N;
	}
	else if (!strcmp(name,"db6")){
        copy_reverse(db6, N, lp1);
        qmf_wrev(db6, N, hp1);
        copy(db6, N, lp2);
        qmf_even(db6, N, hp2);

        return N;
	}
	else if (!strcmp(name,"db7")){
        copy_reverse(db7, N, lp1);
        qmf_wrev(db7, N, hp1);
        copy(db7, N, lp2);
        qmf_even(db7, N, hp2);

        return N;
	}
	else if (!strcmp(name,"db8")){
        copy_reverse(db8, N, lp1);
        qmf_wrev(db8, N, hp1);
        copy(db8, N, lp2);
        qmf_even(db8, N, hp2);

        return N;
	}
	else if (!strcmp(name,"db9")){
        copy_reverse(db9, N, lp1);
        qmf_wrev(db9, N, hp1);
        copy(db9, N, lp2);
        qmf_even(db9, N, hp2);

        return N;
	}

	else if (!strcmp(name,"db10")){
        copy_reverse(db10, N, lp1);
        qmf_wrev(db10, N, hp1);
        copy(db10, N, lp2);
        qmf_even(db10, N, hp2);

        return N;
	}

	else if (!strcmp(name,"db11")){
        copy_reverse(db11, N, lp1);
        qmf_wrev(db11, N, hp1);
        copy(db11, N, lp2);
        qmf_even(db11, N, hp2);

        return N;
	}
	else if (!strcmp(name,"db12")){
        copy_reverse(db12, N, lp1);
        qmf_wrev(db12, N, hp1);
        copy(db12, N, lp2);
        qmf_even(db12, N, hp2);

        return N;
	}

	else if (!strcmp(name,"db13")){
        copy_reverse(db13, N, lp1);
        qmf_wrev(db13, N, hp1);
        copy(db13, N, lp2);
        qmf_even(db13, N, hp2);

        return N;
	}

	else if (!strcmp(name,"db14")){
        copy_reverse(db14, N, lp1);
        qmf_wrev(db14, N, hp1);
        copy(db14, N, lp2);
        qmf_even(db14, N, hp2);

        return N;
	}
	else if (!strcmp(name,"db15")){
        copy_reverse(db15, N, lp1);
        qmf_wrev(db15, N, hp1);
        copy(db15, N, lp2);
        qmf_even(db15, N, hp2);

        return N;
	}
    else if (!strcmp(name, "db16")){
        copy_reverse(db16, N, lp1);
        qmf_wrev(db16, N, hp1);
        copy(db16, N, lp2);
        qmf_even(db16, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db17")){
        copy_reverse(db17, N, lp1);
        qmf_wrev(db17, N, hp1);
        copy(db17, N, lp2);
        qmf_even(db17, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db18")){
        copy_reverse(db18, N, lp1);
        qmf_wrev(db18, N, hp1);
        copy(db18, N, lp2);
        qmf_even(db18, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db19")){
        copy_reverse(db19, N, lp1);
        qmf_wrev(db19, N, hp1);
        copy(db19, N, lp2);
        qmf_even(db19, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db20")){
        copy_reverse(db20, N, lp1);
        qmf_wrev(db20, N, hp1);
        copy(db20, N, lp2);
        qmf_even(db20, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db21")){
        copy_reverse(db21, N, lp1);
        qmf_wrev(db21, N, hp1);
        copy(db21, N, lp2);
        qmf_even(db21, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db22")){
        copy_reverse(db22, N, lp1);
        qmf_wrev(db22, N, hp1);
        copy(db22, N, lp2);
        qmf_even(db22, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db23")){
        copy_reverse(db23, N, lp1);
        qmf_wrev(db23, N, hp1);
        copy(db23, N, lp2);
        qmf_even(db23, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db24")){
        copy_reverse(db24, N, lp1);
        qmf_wrev(db24, N, hp1);
        copy(db24, N, lp2);
        qmf_even(db24, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db25")){
        copy_reverse(db25, N, lp1);
        qmf_wrev(db25, N, hp1);
        copy(db25, N, lp2);
        qmf_even(db25, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db26")){
        copy_reverse(db26, N, lp1);
        qmf_wrev(db26, N, hp1);
        copy(db26, N, lp2);
        qmf_even(db26, N, hp2);
        return N;
    }
    else if (!strcmp(name, "db27")){
        copy_reverse(db27, N, lp1);
        qmf_wrev(db27, N, hp1);
        copy(db27, N, lp2);
        qmf_even(db27, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db28")){
        copy_reverse(db28, N, lp1);
        qmf_wrev(db28, N, hp1);
        copy(db28, N, lp2);
        qmf_even(db28, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db29")){
        copy_reverse(db29, N, lp1);
        qmf_wrev(db29, N, hp1);
        copy(db29, N, lp2);
        qmf_even(db29, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db30")){
        copy_reverse(db30, N, lp1);
        qmf_wrev(db30, N, hp1);
        copy(db30, N, lp2);
        qmf_even(db30, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db31")){
        copy_reverse(db31, N, lp1);
        qmf_wrev(db31, N, hp1);
        copy(db31, N, lp2);
        qmf_even(db31, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db32")){
        copy_reverse(db32, N, lp1);
        qmf_wrev(db32, N, hp1);
        copy(db32, N, lp2);
        qmf_even(db32, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db33")){
        copy_reverse(db33, N, lp1);
        qmf_wrev(db33, N, hp1);
        copy(db33, N, lp2);
        qmf_even(db33, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db34")){
        copy_reverse(db34, N, lp1);
        qmf_wrev(db34, N, hp1);
        copy(db34, N, lp2);
        qmf_even(db34, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db35")){
        copy_reverse(db35, N, lp1);
        qmf_wrev(db35, N, hp1);
        copy(db35, N, lp2);
        qmf_even(db35, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db36")){
        copy_reverse(db36, N, lp1);
        qmf_wrev(db36, N, hp1);
        copy(db36, N, lp2);
        qmf_even(db36, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db37")){
        copy_reverse(db37, N, lp1);
        qmf_wrev(db37, N, hp1);
        copy(db37, N, lp2);
        qmf_even(db37, N, hp2);

        return N;
    }
    else if (!strcmp(name, "db38")){
        copy_reverse(db38, N, lp1);
        qmf_wrev(db38, N, hp1);
        copy(db38, N, lp2);
        qmf_even(db38, N, hp2);

        return N;
    }
	else if (!strcmp(name,"bior1.1")){
        copy_reverse(hm1_11, N, lp1);
        qmf_wrev(h1 + 4, N, hp1);
        copy(h1 + 4, N, lp2);
        qmf_even(hm1_11, N, hp2);
        return N;
	}

	else if (!strcmp(name,"bior1.3")){
        copy_reverse(hm1_13, N, lp1);
        qmf_wrev(h1 + 2, N, hp1);
        copy(h1 + 2, N, lp2);
        qmf_even(hm1_13, N, hp2);
        return N;
	}

	else if (!strcmp(name,"bior1.5")){
        copy_reverse(hm1_15, N, lp1);
        qmf_wrev(h1, N, hp1);
        copy(h1, N, lp2);
        qmf_even(hm1_15, N, hp2);
        return N;
	}

	else if (!strcmp(name,"bior2.2")){
        copy_reverse(hm2_22, N, lp1);
        qmf_wrev(h2 + 6, N, hp1);
        copy(h2 + 6, N, lp2);
        qmf_even(hm2_22, N, hp2);
        return N;
	}
    else if (!strcmp(name, "bior2.4")){
        copy_reverse(hm2_24, N, lp1);
        qmf_wrev(h2 + 4, N, hp1);
        copy(h2 + 4, N, lp2);
        qmf_even(hm2_24, N, hp2);
        return N;
    }
	else if (!strcmp(name,"bior2.6")){
        copy_reverse(hm2_26, N, lp1);
        qmf_wrev(h2 + 2, N, hp1);
        copy(h2 + 2, N, lp2);
        qmf_even(hm2_26, N, hp2);
        return N;
	}
	else if (!strcmp(name,"bior2.8")){
        copy_reverse(hm2_28, N, lp1);
        qmf_wrev(h2, N, hp1);
        copy(h2, N, lp2);
        qmf_even(hm2_28, N, hp2);
        return N;
	}

	else if (!strcmp(name,"bior3.1")){
        copy_reverse(hm3_31, N, lp1);
        qmf_wrev(h3 + 8, N, hp1);
        copy(h3 + 8, N, lp2);
        qmf_even(hm3_31, N, hp2);
        return N;
	}
    else if (!strcmp(name, "bior3.3")){
        copy_reverse(hm3_33, N, lp1);
        qmf_wrev(h3 + 6, N, hp1);
        copy(h3 + 6, N, lp2);
        qmf_even(hm3_33, N, hp2);
        return N;
    }
	else if (!strcmp(name,"bior3.5")){
        copy_reverse(hm3_35, N, lp1);
        qmf_wrev(h3 + 4, N, hp1);
        copy(h3 + 4, N, lp2);
        qmf_even(hm3_35, N, hp2);
        return N;
	}
    else if (!strcmp(name, "bior3.7")){
        copy_reverse(hm3_37, N, lp1);
        qmf_wrev(h3 + 2, N, hp1);
        copy(h3 +2, N, lp2);
        qmf_even(hm3_37, N, hp2);
        return N;
    }
	else if (!strcmp(name,"bior3.9")){
        copy_reverse(hm3_39, N, lp1);
        qmf_wrev(h3, N, hp1);
        copy(h3, N, lp2);
        qmf_even(hm3_39, N, hp2);
        return N;
	}
	else if (!strcmp(name,"bior4.4")){
        copy_reverse(hm4_44, N, lp1);
        qmf_wrev(h4, N, hp1);
        copy(h4, N, lp2);
        qmf_even(hm4_44, N, hp2);
        return N;
	}
    else if (!strcmp(name, "bior5.5")){
        copy_reverse(hm5_55, N, lp1);
        qmf_wrev(h5, N, hp1);
        copy(h5, N, lp2);
        qmf_even(hm5_55, N, hp2);
        return N;
    }
	else if (!strcmp(name,"bior6.8")){
        copy_reverse(hm6_68, N, lp1);
        qmf_wrev(h6, N, hp1);
        copy(h6, N, lp2);
        qmf_even(hm6_68, N, hp2);
        return N;
	}
    else if (!strcmp(name, "rbior1.1")){
        copy_reverse(h1 + 4, N, lp1);
        qmf_wrev(hm1_11, N, hp1);
        copy(hm1_11, N, lp2);
        qmf_even(h1 + 4, N, hp2);
        return N;
    }

    else if (!strcmp(name, "rbior1.3")){
        copy_reverse(h1 + 2, N, lp1);
        qmf_wrev(hm1_13, N, hp1);
        copy(hm1_13, N, lp2);
        qmf_even(h1 + 2, N, hp2);
        return N;
    }

    else if (!strcmp(name, "rbior1.5")){
        copy_reverse(h1, N, lp1);
        qmf_wrev(hm1_15, N, hp1);
        copy(hm1_15, N, lp2);
        qmf_even(h1, N, hp2);
        return N;
    }

    else if (!strcmp(name, "rbior2.2")){
        copy_reverse(h2 + 6, N, lp1);
        qmf_wrev(hm2_22, N, hp1);
        copy(hm2_22, N, lp2);
        qmf_even(h2 + 6, N, hp2);
        return N;
    }
    else if (!strcmp(name, "rbior2.4")){
        copy_reverse(h2 + 4, N, lp1);
        qmf_wrev(hm2_24, N, hp1);
        copy(hm2_24, N, lp2);
        qmf_even(h2 + 4, N, hp2);
        return N;
    }
    else if (!strcmp(name, "rbior2.6")){
        copy_reverse(h2 + 2, N, lp1);
        qmf_wrev(hm2_26, N, hp1);
        copy(hm2_26, N, lp2);
        qmf_even(h2 + 2, N, hp2);
        return N;
    }
    else if (!strcmp(name, "rbior2.8")){
        copy_reverse(h2, N, lp1);
        qmf_wrev(hm2_28, N, hp1);
        copy(hm2_28, N, lp2);
        qmf_even(h2, N, hp2);
        return N;
    }

    else if (!strcmp(name, "rbior3.1")){
        copy_reverse(h3 + 8, N, lp1);
        qmf_wrev(hm3_31, N, hp1);
        copy(hm3_31, N, lp2);
        qmf_even(h3 + 8, N, hp2);
        return N;
    }
    else if (!strcmp(name, "rbior3.3")){
        copy_reverse(h3 + 6, N, lp1);
        qmf_wrev(hm3_33, N, hp1);
        copy(hm3_33, N, lp2);
        qmf_even(h3 + 6, N, hp2);
        return N;
    }
    else if (!strcmp(name, "rbior3.5")){
        copy_reverse(h3 + 4, N, lp1);
        qmf_wrev(hm3_35, N, hp1);
        copy(hm3_35, N, lp2);
        qmf_even(h3 + 4, N, hp2);
        return N;
    }
    else if (!strcmp(name, "rbior3.7")){
        copy_reverse(h3 + 2, N, lp1);
        qmf_wrev(hm3_37, N, hp1);
        copy(hm3_37, N, lp2);
        qmf_even(h3 + 2, N, hp2);
        return N;
    }
    else if (!strcmp(name, "rbior3.9")){
        copy_reverse(h3, N, lp1);
        qmf_wrev(hm3_39, N, hp1);
        copy(hm3_39, N, lp2);
        qmf_even(h3, N, hp2);
        return N;
    }
    else if (!strcmp(name, "rbior4.4")){
        copy_reverse(h4, N, lp1);
        qmf_wrev(hm4_44, N, hp1);
        copy(hm4_44, N, lp2);
        qmf_even(h4, N, hp2);
        return N;
    }
    else if (!strcmp(name, "rbior5.5")){
        copy_reverse(h5, N, lp1);
        qmf_wrev(hm5_55, N, hp1);
        copy(hm5_55, N, lp2);
        qmf_even(h5, N, hp2);
        return N;
    }
    else if (!strcmp(name, "rbior6.8")){
        copy_reverse(h6, N, lp1);
        qmf_wrev(hm6_68, N, hp1);
        copy(hm6_68, N, lp2);
        qmf_even(h6, N, hp2);
        return N;
    }
	else if (!strcmp(name,"coif1")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif1, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
	}
	else if (!strcmp(name,"coif2")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif2, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
	}
	else if (!strcmp(name,"coif3")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif3, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
	}
	else if (!strcmp(name,"coif4")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif4, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
	}
	else if (!strcmp(name,"coif5")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif5, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
	}
    else if (!strcmp(name, "coif6")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif6, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
    else if (!strcmp(name, "coif7")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif7, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
    else if (!strcmp(name, "coif8")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif8, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
    else if (!strcmp(name, "coif9")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif9, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
    else if (!strcmp(name, "coif10")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif10, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
    else if (!strcmp(name, "coif11")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif11, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
    else if (!strcmp(name, "coif12")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif12, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
    else if (!strcmp(name, "coif13")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif13, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
    else if (!strcmp(name, "coif14")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif14, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
    else if (!strcmp(name, "coif15")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif15, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
    else if (!strcmp(name, "coif16")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif16, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
    else if (!strcmp(name, "coif17")){
        double *coeffTemp;
        coeffTemp = (double*)malloc(N*sizeof(double));

        copy(coif17, N, coeffTemp);
        for (i = 0; i < N; ++i) {
            coeffTemp[i] *= M_SQRT2;
        }

        copy_reverse(coeffTemp, N, lp1);
        qmf_wrev(coeffTemp, N, hp1);
        copy(coeffTemp, N, lp2);
        qmf_even(coeffTemp, N, hp2);
        free(coeffTemp);

        return N;
    }
	else if (!strcmp(name,"sym2")){
        copy_reverse(sym2, N, lp1);
        qmf_wrev(sym2, N, hp1);
        copy(sym2, N, lp2);
        qmf_even(sym2, N, hp2);
        return N;
	}

	else if (!strcmp(name,"sym3")){
        copy_reverse(sym3, N, lp1);
        qmf_wrev(sym3, N, hp1);
        copy(sym3, N, lp2);
        qmf_even(sym3, N, hp2);
        return N;
	}

	else if (!strcmp(name,"sym4")){
        copy_reverse(sym4, N, lp1);
        qmf_wrev(sym4, N, hp1);
        copy(sym4, N, lp2);
        qmf_even(sym4, N, hp2);
        return N;
	}

	else if (!strcmp(name,"sym5")){
        copy_reverse(sym5, N, lp1);
        qmf_wrev(sym5, N, hp1);
        copy(sym5, N, lp2);
        qmf_even(sym5, N, hp2);
        return N;
	}

	else if (!strcmp(name,"sym6")){
        copy_reverse(sym6, N, lp1);
        qmf_wrev(sym6, N, hp1);
        copy(sym6, N, lp2);
        qmf_even(sym6, N, hp2);
        return N;
	}

	else if (!strcmp(name,"sym7")){
        copy_reverse(sym7, N, lp1);
        qmf_wrev(sym7, N, hp1);
        copy(sym7, N, lp2);
        qmf_even(sym7, N, hp2);
        return N;
	}

	else if (!strcmp(name,"sym8")){
        copy_reverse(sym8, N, lp1);
        qmf_wrev(sym8, N, hp1);
        copy(sym8, N, lp2);
        qmf_even(sym8, N, hp2);
        return N;
	}

	else if (!strcmp(name,"sym9")){
        copy_reverse(sym9, N, lp1);
        qmf_wrev(sym9, N, hp1);
        copy(sym9, N, lp2);
        qmf_even(sym9, N, hp2);
        return N;
	}

	else if (!strcmp(name,"sym10")){
        copy_reverse(sym10, N, lp1);
        qmf_wrev(sym10, N, hp1);
        copy(sym10, N, lp2);
        qmf_even(sym10, N, hp2);
        return N;
	}
    else if (!strcmp(name, "sym11")){
        copy_reverse(sym11, N, lp1);
        qmf_wrev(sym11, N, hp1);
        copy(sym11, N, lp2);
        qmf_even(sym11, N, hp2);
        return N;
    }
    else if (!strcmp(name, "sym12")){
        copy_reverse(sym12, N, lp1);
        qmf_wrev(sym12, N, hp1);
        copy(sym12, N, lp2);
        qmf_even(sym12, N, hp2);
        return N;
    }
    else if (!strcmp(name, "sym13")){
        copy_reverse(sym13, N, lp1);
        qmf_wrev(sym13, N, hp1);
        copy(sym13, N, lp2);
        qmf_even(sym13, N, hp2);
        return N;
    }
    else if (!strcmp(name, "sym14")){
        copy_reverse(sym14, N, lp1);
        qmf_wrev(sym14, N, hp1);
        copy(sym14, N, lp2);
        qmf_even(sym14, N, hp2);
        return N;
    }
    else if (!strcmp(name, "sym15")){
        copy_reverse(sym15, N, lp1);
        qmf_wrev(sym15, N, hp1);
        copy(sym15, N, lp2);
        qmf_even(sym15, N, hp2);
        return N;
    }
    else if (!strcmp(name, "sym16")){
        copy_reverse(sym16, N, lp1);
        qmf_wrev(sym16, N, hp1);
        copy(sym16, N, lp2);
        qmf_even(sym16, N, hp2);
        return N;
    }
    else if (!strcmp(name, "sym17")){
        copy_reverse(sym17, N, lp1);
        qmf_wrev(sym17, N, hp1);
        copy(sym17, N, lp2);
        qmf_even(sym17, N, hp2);
        return N;
    }
    else if (!strcmp(name, "sym18")){
        copy_reverse(sym18, N, lp1);
        qmf_wrev(sym18, N, hp1);
        copy(sym18, N, lp2);
        qmf_even(sym18, N, hp2);
        return N;
    }
    else if (!strcmp(name, "sym19")){
        copy_reverse(sym19, N, lp1);
        qmf_wrev(sym19, N, hp1);
        copy(sym19, N, lp2);
        qmf_even(sym19, N, hp2);
        return N;
    }
    else if (!strcmp(name, "sym20")){
        copy_reverse(sym20, N, lp1);
        qmf_wrev(sym20, N, hp1);
        copy(sym20, N, lp2);
        qmf_even(sym20, N, hp2);
        return N;
    }
    else if (!strcmp(name, "meyer")){
        copy_reverse(meyer, N, lp1);
        qmf_wrev(meyer, N, hp1);
        copy(meyer, N, lp2);
        qmf_even(meyer, N, hp2);
        return N;
    }

	else {
		printf("\n Filter Not in Database \n");
		return -1;
	}

	return 0;
}
