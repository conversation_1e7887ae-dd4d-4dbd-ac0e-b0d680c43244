#include <jni.h>

#include <vector>
#include "filter.h"
#include <cstdlib>
#include <cstdio>
#include <cmath>

#include <vector>
#include <cstring>       // für std::memcpy
extern "C" {
#include "wavelib/header/wavelib.h"  // dh. wave_init, wave_free
#include "wavelib/src/cwt.h"       // dh. wt_init, dwt, idwt, wt_free, setDWTExtension
}

// Detrend-Funktion: input & output haben Länge N
void detrend_with_wavelib(const double* input, double* output,
                          int N, int levels,
                          const char* waveletName = "db4",
                          const char* mode        = "sym")
{
    // 1. Copy input to a mutable buffer
    std::vector<double> buffer(input, input + N);

    // 2. Initialize wavelet object
    wave_object wav = wave_init(const_cast<char*>(waveletName));
    if (!wav) {
        // Handle error: invalid waveletName
        return;
    }

    // 3. Initialize DWT object
    wt_object wt = wt_init(wav, const_cast<char*>("dwt"), N, levels);
    if (!wt) {
        wave_free(wav);
        // Handle error: levels too large or allocation failure
        return;
    }

    // 4. Set extension/padding mode
    setDWTExtension(wt, const_cast<char*>(mode));

    // 5. Forward DWT on mutable buffer
    dwt(wt, buffer.data());

    // 6. Zero out approximation coefficients (first wt->length[0] values)
    int approxLen = std::min(wt->length[0], wt->outlength);
    for (int i = 0; i < approxLen; ++i) {
        wt->output[i] = 0.0;
    }

    // 7. Inverse DWT into output buffer
    idwt(wt, output);

    // 8. Clean up resources
    wt_free(wt);
    wave_free(wav);
}

// Entry function called from Java
extern "C"
JNIEXPORT jfloatArray JNICALL
Java_com_clj_blesamplePCBA_SignalProcessor_runNativePipeline(JNIEnv *env, jclass clazz, jfloatArray ppg_in, jfloatArray acc_in, jfloat fs) {
    jsize len = env->GetArrayLength(ppg_in);

    std::vector<float> ppg(len);
    std::vector<float> acc(len);

    env->GetFloatArrayRegion(ppg_in, 0, len, ppg.data());
    env->GetFloatArrayRegion(acc_in, 0, len, acc.data());

    // --- Create bandpass filter instance ---
    BWBandPass* filter = create_bw_band_pass_filter(4, fs, 0.6f, 4.0f); // 4th order, fs from Java

    // --- Process PPG signal through bandpass filter ---
    for (int i = 0; i < len; i++) {
        ppg[i] = bw_band_pass(filter, ppg[i]);
    }

    // --- Clean up ---
    free_bw_band_pass(filter);

    // jfloatArray output = env->NewFloatArray(len);
    //env->SetFloatArrayRegion(output, 0, len, ppg.data());
    // return output;
    std::vector<double> ppg_d(len), out_d(len);
    for (int i = 0; i < len; ++i)
        ppg_d[i] = static_cast<double>(ppg[i]);

    // 5) Wavelet-Detrend in double
    detrend_with_wavelib(
            ppg_d.data(),    // input double*
            out_d.data(),    // output double*
            static_cast<int>(len),
            /*levels*/ 4,
            /*waveletName*/ "db4",
            /*mode*/        "sym"
    );

    // 6) Zurück in float
    for (int i = 0; i < len; ++i)
        ppg[i] = static_cast<float>(out_d[i]);

    // 7) Ergebnis zurückgeben
    jfloatArray output = env->NewFloatArray(len);
    env->SetFloatArrayRegion(output, 0, len, ppg.data());
    return output;
}
