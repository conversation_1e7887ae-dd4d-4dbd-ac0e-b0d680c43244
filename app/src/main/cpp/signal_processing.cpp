#include "signal_processing.h"
#include <jni.h>

template <int filterOrder>
void SignalProcessor::configure(double fs, double fLow, double fHigh) {
  bp_.setup(fs, fLow, fHigh);
}

template <int filterOrder>
void SignalProcessor::bandpass_filter(std::span<float>& signal) {
  bp_.filterInPlace(signal.data(), signal.size());
}

// Entry function called from Java
extern "C" JNIEXPORT jfloatArray JNICALL
Java_com_example_myapp_SignalProcessor_runNativePipeline(JNIEnv* env,
                                                         jobject thiz,
                                                         jfloatArray ppg_in,
                                                         jfloatArray acc_in,
                                                         jfloat fs) {
  jsize len = env->GetArrayLength(ppg_in);

  std::array<float, 50> ppg;
  std::array<float, 50> acc;

  env->GetFloatArrayRegion(ppg_in, 0, len, ppg.data());
  env->GetFloatArrayRegion(acc_in, 0, len, acc.data());

  high_pass_filter(ppg);
  butter_bandpass_filter(ppg, fs, 0.6f, 4.0f);
  rls_filter(ppg, acc);

  jfloatArray output = env->NewFloatArray(len);
  env->SetFloatArrayRegion(output, 0, len, ppg.data());
  return output;
}
