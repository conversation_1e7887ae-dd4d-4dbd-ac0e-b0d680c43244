ext {
    var = 'C:\\Users\\<USER>\\AndroidStudioProjects\\FastBle\\app\\build\\outputs\\apk\\debug\\app-debug.apk'
}
apply plugin: 'com.android.application'

android {
    signingConfigs {
        config {
            storePassword 'andy315797'
            keyAlias 'andy315797'
            keyPassword 'andy315797'
            storeFile file('C:\\Users\\<USER>\\AndroidStudioProjects\\FastBle\\app\\build\\outputs\\apk\\release\\app-release-unsigned.apk')
        }
    }
    compileSdkVersion 35
    defaultConfig {
        applicationId "com.clj.blesamplePCBA"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 235
        versionName "2.3.3"
        vectorDrawables.useSupportLibrary = true
        externalNativeBuild {
            cmake {
                cppFlags ''
            }
        }
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }
    externalNativeBuild {
        cmake {
            path 'src/main/cpp/CMakeLists.txt'
            version '3.22.1'
        }
    }
    buildTypes {
//        release {
//            minifyEnabled false
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//        }
        debug {
            debuggable true
        }
    }
    namespace 'com.clj.blesamplePCBA'
    lint {
        disable 'MissingTranslation'
    }
}

repositories {
    // CHARTING
    maven { url 'https://jitpack.io' }
}

dependencies {
    //noinspection GradleCompatible
    implementation 'com.android.support:design:27.1.1'
    //noinspection GradleCompatible
    implementation 'com.android.support:support-v4:27.1.1'
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.google.android.material:material:1.12.0'
    testImplementation 'junit:junit:4.12'
    //noinspection GradleCompatible
    implementation 'com.android.support:appcompat-v7:27.1.1'
    implementation project(':FastBleLib')
    implementation 'com.android.support.constraint:constraint-layout:1.1.3'

    //CHARTING
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'

}